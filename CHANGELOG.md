# Changelog

All notable changes to the Vape-Hybrid Coupon Site will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [5.6.0] - 2025-06-13

### 🔍 SEO Improvements
- **Enhanced Meta Tag Generation System**
  - Optimized titles and descriptions for better search engine visibility
  - Competitive analysis-based SEO meta templates
  - Social proof elements in meta descriptions
  - Structured data improvements for search results

### 🚀 New Components
- **Brand Page Components**
  - Added `BrandActivity` for social proof elements
  - Added `BrandDealsWrapper` for improved deal presentation
  - Added `BrandRelatedSections` for better internal linking
  - Added `ExpandableBrandInfo` for better user engagement and content discovery

- **Category Page Components**
  - Added `CategoryAbout` for rich content sections
  - Added `CategoryActivity` for social proof elements
  - Added `CategoryDealListCard` for improved deal presentation
  - Added `CategoryFAQ` for enhanced user experience and SEO
  - Added `CategoryRelatedSections` for better internal linking
  - Added `ExpandableCategoryInfo` for better user engagement

### 🛠️ Technical Improvements
- **Verification System**
  - Centralized verification time utilities
  - Consistent time display across components
  - Algorithmic verification timestamps for better user trust

- **Template Optimizations**
  - Improved brand page template structure
  - Enhanced category page template layout
  - Optimized merchant page components

### 📁 New Files
- `src/utils/seo-meta-generators.ts`
- `src/utils/verification-time.ts`
- `src/components/Brand/BrandActivity.tsx`
- `src/components/Brand/BrandDealsWrapper.tsx`
- `src/components/Brand/BrandRelatedSections.tsx`
- `src/components/Brand/ExpandableBrandInfo.tsx`
- `src/components/Category/CategoryAbout.tsx`
- `src/components/Category/CategoryActivity.tsx`
- `src/components/Category/CategoryDealListCard.tsx`
- `src/components/Category/CategoryFAQ.tsx`
- `src/components/Category/CategoryRelatedSections.tsx`
- `src/components/Category/ExpandableCategoryInfo.tsx`

### 📊 Analytics & Conversion
- Enhanced tracking of user engagement with expandable sections
- Improved coupon conversion tracking
- Better social proof elements for increased trust

## [5.5.0] - 2025-06-12

### 🚀 Major Features
- **Automated Coupon Alerting System**
  - Toast notifications for newsletter signups
  - Enhanced newsletter API with source tracking
  - Automated email alerts for new coupons
  - Revenue-protected email system with `/go/` affiliate links

### 🎨 UI/UX Improvements
- **Brand Page Components**
  - Added `BrandEmailCaptureForm`
  - Added `BrandFAQ`
  - Added `BrandAbout`

- **Enhanced Merchant Pages**
  - Comprehensive two-column layout
  - Improved mobile responsiveness
  - Better visual hierarchy

### 🛠️ Technical Improvements
- **Email System**
  - Personalized confirmation emails
  - Smart anti-spam filtering
  - Frequency management
  - Preference tracking

### 📁 New Files
- `src/components/Brand/BrandAbout.tsx`
- `src/components/Brand/BrandEmailCaptureForm.tsx`
- `src/components/Brand/BrandFAQ.tsx`
- `src/components/Category/CategoryEmailCaptureForm.tsx`
- `src/pages/api/alerts/trigger-new-coupon.ts`

### 📢 Documentation
- Comprehensive documentation for alerting system architecture
- API integration guidelines for coupon alerts

### 🔗 SEO Improvements
- Improved engagement metrics through email alerts
- Better content discovery for new coupon offers
- Enhanced user retention via notification system

## [5.4.0] - 2025-06-12

### 🚀 Major Features
- **Merchant Page Overhaul**
  - Complete redesign with dynamic content and SEO optimization
  - Specialized `MerchantDealListCard` component (175px height)
  - Expandable merchant information with vape-focused copy
  - Working newsletter integration with Resend

### 🎨 UI/UX Improvements
- **Merchant Components**
  - Fixed icon sizing (16px) and spacing
  - Enhanced card layouts with optimized spacing
  - Added best discount badge with gradient styling
  - Implemented accordion FAQ with 17px bold headings
  - Explore categories and brands sections

### 📊 Dynamic Content System
- **Merchant-Specific Features**
  - Store policies based on merchant type
  - Consistent random ratings (3.5-5.0 stars)
  - Recent verifications using deal titles
  - Dynamic activity stats and engagement metrics

### 🔍 SEO & Content
- **Optimizations**
  - Original vape-focused content
  - Conversational English writing style
  - Bold/italic formatting for key points
  - Enhanced breadcrumbs and navigation
  - Schema.org markup for better search visibility

### 🛠 Technical Improvements
- **New Components**
  - `MerchantDealListCard.tsx` - Specialized deal cards
  - `ExpandableMerchantInfo.tsx` - Collapsible content
  - `CouponStatsDashboard.tsx` - Merchant statistics
  - `CouponVerificationBadge.tsx` - Trust indicators
  - `EmailCaptureForm.tsx` - Newsletter integration

- **Enhancements**
  - Fixed double percentage signs in stats
  - Proper import/export handling
  - Working popup functionality
  - Go/ link implementation for affiliate protection

### 🎨 Styling Updates
- **Design System**
  - 11px notice bar text with theme support
  - Black text in newsletter buttons
  - Enhanced sidebar contrast
  - Responsive layouts (730px+ right column)
  - Professional merchant page structure

### 📁 New Files
```
src/components/Merchant/
  AboutMerchant.tsx
  CouponStatsDashboard.tsx
  CouponVerificationBadge.tsx
  EmailCaptureForm.tsx
  EnhancedCouponCard.tsx
  ExpandableMerchantInfo.tsx
  MerchantDealListCard.tsx
  MerchantFAQ.tsx
  RelatedMerchants.tsx
  StorePolicies.tsx

src/pages/coupons/merchants/
  [id].astro
  index.astro

src/utils/
  coupon-stats.ts
  schema-markup.ts
```

### ✅ Expected Results
- Fully functional merchant pages with dynamic content
- Improved user engagement with expandable sections
- Better SEO with structured data and optimized content
- Consistent styling and layout across all merchant pages
- Working newsletter signup and coupon interactions

### 🧪 Testing Coverage
- Merchant page loading and rendering
- Expandable sections functionality
- Newsletter form submission
- Coupon reveal and copy functionality
- Mobile responsiveness
- SEO elements and structured data

## [5.3.0] - 2025-06-11

### 🚀 Major Features
- **React Component Suite**
  - Created `CouponActionButton` - Unified button with code reveal and popup/redirect logic
  - Created `CouponVotingButton` - Interactive voting with visual feedback
  - Created `CouponBookmarkButton` - Enhanced bookmark functionality
  - Created `CouponPagePopup` - Popup detection and modal display system

- **Enhanced User Experience**
  - Single button design for coupon actions
  - Vertical layout for better content flow
  - Mobile-responsive interactions
  - Consistent behavior with OptimizedDealCard

### 🐛 Critical Fixes
- **Popup & Redirect System**
  - Fixed popup behavior to open on the same page
  - Added proper modal display for coupon details
  - Ensured correct redirect flow to `/go/[id]`
  - Matched functionality with OptimizedDealCard

### 🎨 UI/UX Improvements
- **Visual Feedback**
  - Added reveal animations for coupon codes
  - Improved button states (hover, active, disabled)
  - Better icon alignment and spacing
  - Touch-friendly interactive elements

- **Voting System**
  - Dual voting options (thumbs up/down)
  - Visual feedback for votes (green/red indicators)
  - One-vote restriction per deal
  - Analytics integration for tracking

### ⚡ Technical Enhancements
- **React Integration**
  - Proper state management with hooks
  - Client-side hydration with `client:load`
  - TypeScript type safety throughout
  - Component-based architecture

- **State Management**
  - localStorage integration for persistence
  - Synchronized state across components
  - Optimized re-renders

### 📁 New Components
- `src/components/CouponPage/CouponActionButton.tsx`
- `src/components/CouponPage/CouponBookmarkButton.tsx`
- `src/components/CouponPage/CouponPagePopup.tsx`
- `src/components/CouponPage/CouponVotingButton.tsx`
- `src/utils/couponUrlUtils.ts`

### ✅ Expected Results
- Seamless popup and redirect behavior
- Full interactive functionality on coupon pages
- Consistent experience across all components
- Improved performance with optimized renders
- Enhanced mobile experience

### 🧪 Testing Coverage
- Coupon code reveal and copy functionality
- Voting system (up/down votes)
- Bookmark persistence
- Popup and redirect behavior
- Mobile responsiveness
- Cross-browser compatibility

## [5.2.0] - 2025-06-11

### 🚀 Major Features
- **Sticky Navigation**
  - Completely rewritten sticky controls JavaScript logic
  - Added proper state management with `showSticky()` and `hideSticky()`
  - Sticky controls now hidden by default and only activate when needed
  - Optimized for desktop devices only (disabled on mobile <768px)

- **Individual Coupon Pages**
  - Complete redesign with competitor-inspired features
  - Hidden coupon codes with click-to-reveal functionality
  - Voting system with thumbs up
  - Bookmark system with heart icon
  - Copy-to-clipboard functionality
  - Improved URL slug generation without % encoding
  - /go/ tracking links implementation

### 🐛 Critical Fixes
- **Font Loading & CSS**
  - Fixed font loading errors on coupon pages
  - Added missing CSS utilities for `line-clamp-*` and `deal-card-title`
  - Ensured proper text rendering and truncation

- **FilterDrawer**
  - Fixed "Failed to construct 'URL': Invalid URL" TypeError
  - Enhanced URL parsing with fallback strategies
  - Added browser environment checks for SSR compatibility

- **Coupon Counts**
  - Fixed "0 Coupons Available" showing despite having deals
  - Now using correct `totalCount` instead of `dealCount`
  - Applied to brand and category pages

### 🎨 Design Improvements
- Added light mode background color `#fbfafc` to all coupon pages
- Centered headers and breadcrumbs on category pages
- Improved mobile-responsive navigation behavior
- Updated CSS variables for consistent background colors
- Enhanced overall visual hierarchy and spacing

### ⚡ Performance Optimizations
- Implemented `requestAnimationFrame` for smooth scroll handling
- Added passive event listeners for better scroll performance
- Optimized state tracking to prevent unnecessary DOM updates
- Improved mobile experience with touch-friendly elements

### 🛠️ Technical Improvements
- **URL Handling**
  - Enhanced URL construction in FilterForm
  - Added multiple fallback strategies for URL parsing
  - Improved slug generation for cleaner URLs

- **Error Handling**
  - Added comprehensive error boundaries
  - Implemented graceful degradation for edge cases
  - Enhanced browser environment detection

- **Mobile Experience**
  - Completely disabled sticky controls on mobile
  - Optimized touch targets for better usability
  - Improved responsive design across all breakpoints

### 📁 Files Modified
- `src/pages/coupons/index.astro` - Main coupons page with sticky controls
- `src/pages/coupons/brands/[slug].astro` - Brand coupon pages
- `src/pages/coupons/categories/[slug].astro` - Category coupon pages
- `src/pages/coupon/[slug].astro` - Individual coupon pages
- `src/pages/deal/[id].astro` - Deal redirect with clean slug generation
- `src/components/DesignSystem/FilterForm.tsx` - URL construction fix
- `src/styles/main.css` - Added missing CSS utilities
- `src/styles/css-variables.css` - Updated background colors

### ✅ Expected Results
- Working filter drawer without crashes
- Single sorting panel with proper sticky behavior (desktop only)
- Clean URLs without % encoding
- Professional individual coupon pages with interactive features
- Accurate coupon counts on all pages
- Consistent light mode background color
- Enhanced mobile experience

### 🧪 Testing Coverage
- Filter drawer functionality on all coupon pages
- Sticky navigation behavior on desktop vs mobile
- Individual coupon page interactions (reveal codes, voting, bookmarks)
- URL slug generation and redirects
- Coupon count accuracy on brand/category pages
- Mobile responsiveness and touch interactions

## [5.1.0] - 2025-06-11

### 🎨 UI/UX Enhancements
- **Menu System Overhaul**
  - Fully visible text in both light and dark modes
  - Clickable menu items with proper navigation
  - Smooth hover and active state animations
  - Professional backdrop with close functionality
  - Escape key and backdrop click to close menu
  - WCAG compliant contrast ratios
  - Zero accessibility warnings in menu system

### ♿ Accessibility
- Improved keyboard navigation in all interactive elements
- Enhanced focus states for better visibility
- Proper ARIA labels and roles for screen readers
- Consistent color contrast across all UI elements

### 🐛 Bug Fixes
- Fixed menu text visibility in dark mode
- Resolved navigation issues in mobile menu
- Improved touch targets for better mobile experience
- Fixed z-index issues with dropdown menus

### 🛠️ Technical Improvements
- Refactored menu component for better performance
- Optimized animations for smoother transitions
- Improved state management for menu interactions
- Added proper TypeScript types for menu components

## [5.0.0] - 2025-06-11

### 🚀 Major Features
- Complete deal page redesign with SEO optimization and UI/UX improvements
- Implemented slug-based URLs with 301 redirects for better SEO
- Redesigned CompactDealCard with improved layout and accessibility
- Added dynamic FAQ section with accordion UI and schema markup
- Enhanced reveal button visibility across light/dark themes
- Optimized responsive grid layout for related deals section

### 🏗️ Build & Performance
- Build time optimized: ~13-20 seconds
- Eliminated all React hydration console errors
- Fixed nested anchor tag HTML validation warnings
- Improved component bundle efficiency
- Clean console output with no warnings

### 🎨 UI/UX Enhancements
- Professional, accessible card design
- Perfect responsive behavior across all devices
- Improved readability and visual hierarchy
- Fixed reveal button visibility in light mode
- Proper color contrast for all interactive elements
- Consistent hover states across light/dark themes
- Theme-aware badge colors and backgrounds

### 🔍 SEO Improvements
- Slug-based URLs for better search ranking
- Enhanced meta tags and structured data
- 301 redirects to preserve existing SEO value
- Dynamic FAQ system with schema.org/FAQPage markup
- Dynamic title generation with brand/merchant/discount info
- SEO-optimized descriptions with coupon code context
- Proper canonical URL implementation using slugs
- Enhanced structured data for rich snippets

### 🛠️ Technical Improvements
- Fixed React hydration errors and console warnings
- Improved TypeScript type safety
- Better component organization and code splitting
- Optimized image loading and lazy loading
- Improved error boundaries and fallback UIs

### 📦 Dependencies
- Updated to latest stable versions of all dependencies
- Removed unused packages and dead code

### 🐛 Bug Fixes
- Fixed various accessibility issues
- Resolved layout shifts during page load
- Fixed mobile navigation and touch targets
- Addressed performance bottlenecks in deal listings

### 📝 Documentation
- Updated component documentation
- Added inline code comments for complex logic
- Improved developer documentation

### 🧪 Testing
- Added new test cases for critical paths
- Improved test coverage for UI components
- Added visual regression tests

### 🔧 Configuration
- Updated build configuration for better optimization
- Improved environment variable handling
- Enhanced development tooling

## [3.12.0] - 2025-06-09

### Fixed
- Updated Supabase anon key in wrangler.toml (expired 1999 → 2059)
- Fixed environment variable access for Cloudflare Workers runtime
- Updated all Supabase client functions to use locals.runtime.env
- Fixed RLS policies for public read access on main tables (deals, categories, brands, merchants)
- Created missing sitemap.xml.ts with comprehensive error handling
- Added /coupons/brands to sitemap generator as requested
- Updated all API routes and Astro pages to pass locals parameter
- Resolved "supabaseUrl is required" errors causing blank pages
- All 306 deals now loading correctly on frontend
- Sitemap now accessible at /sitemap.xml with proper SEO structure

## [3.7.1] - 2025-05-20

### Improved
- Enhanced DealCouponModal UI/UX with comprehensive improvements:
  - Added proper spacing to the top and bottom of the modal (pt-8 pb-8)
  - Added spacing above the brand logo (mt-3 mb-4) for better visual hierarchy
  - Reduced description text size for better readability (text-sm)
  - Made "Click the button below to copy the code" text smaller (text-xs) for subtle guidance
  - Vertically stacked voting UI with "Did this code work?" text centered above the voting buttons
  - Converted "Shop at" button to "Get Deal Now" for clearer call-to-action
  - Converted eye icon to a direct link to deal page without view count
  - Rounded all corners to 25px for a more modern appearance
  - Added smooth fade-in and scale animations for modal entry
  - Improved coupon code box with clear visual hierarchy and copy button
  - Enhanced responsive design for better mobile experience

### Fixed
- Eliminated conflicts between script-based modals and React component modals
- Removed redundant button imports and optimized component structure
- Improved toast notifications for coupon code copying with more concise messaging

## [3.5.2] - 2024-07-18

### Fixed
- Fixed React warning about unrecognized `fetchPriority` prop on img elements by using lowercase `fetchpriority` attribute
- Fixed CSS import order in main.css and ITCSS architecture files to comply with PostCSS requirements
- Improved CSS structure by ensuring all @import statements precede other CSS rules
- Enhanced DealImage component to properly handle HTML attributes while maintaining TypeScript compatibility

### Changed
- Updated CSS architecture to better handle imports with proper nesting
- Improved code organization in utility CSS files for better maintainability
- Enhanced type safety in image components with attribute spreading for HTML compatibility

## [3.5.1] - 2024-07-17

### Fixed
- Fixed bookmarks page not displaying saved deals despite showing correct count in navbar
- Fixed bookmark removal functionality not updating the table when clicking the X button
- Improved type handling for bookmark IDs to ensure consistent comparison between localStorage and API data
- Enhanced error handling and debugging in the bookmarks system
- Removed InfoPageHeader dependency from bookmarks page to prevent layout conflicts

### Changed
- Updated API endpoint to handle both string and number IDs for better compatibility
- Improved EmptyBookmarks component with more specific error messages and refresh button
- Enhanced debugging capabilities with detailed console logging
- Added force re-render mechanism to ensure UI updates properly after bookmark removal

## [3.5.0] - 2024-07-16

### Added
- Added CTA button after testimonials section with "View All Deals" text and link to /deals
- Added proper internal padding to all CTA buttons for better text placement

### Changed
- Updated CTA button styling across the site to match design requirements:
  - Changed from gradient to solid blue background (#4a4cd9)
  - Set proper rounded corners with border-radius: 50px
  - Added appropriate padding (px-6 py-2.5)
  - Set minimum width (180px) for consistent button size
  - Ensured text is properly centered
  - Maintained hover effects and transitions
- Increased CTA button size from 125px x 30px to 180px x 40px for better visibility
- Centered all CTA buttons by adding flex layout to container divs
- Reduced background transparency from 80% to 95% for better visibility in all sections
- Changed z-index of pattern background div from z-0 to z-[-1] to prevent it from blocking clicks
- Fixed FAQ section heading to use font-normal instead of font-bold

### Fixed
- Fixed CTA buttons that were left-aligned instead of centered
- Fixed pattern div in Section component that was blocking clicks
- Fixed comparison table background transparency
- Fixed unused variable warnings
- Fixed unused React imports in components

## [3.4.0] - 2024-07-15

### Added
- Added postcss-import plugin to properly handle CSS imports
- Added new ITCSS architecture for CSS organization
- Added standardized button styles with consistent 25px border radius
- Added hover effects with gradient borders for buttons
- Added arrow icon animation that only appears on hover for primary CTAs
- Added blur effect to button glow for a more modern look

### Changed
- Reorganized CSS structure using ITCSS (Inverted Triangle CSS) architecture
- Updated main.css to properly handle imports and Tailwind directives
- Changed secondary CTA buttons to subtle text links for better UX
- Improved logo display in navigation to properly handle light/dark mode
- Fixed navigation icon alignment issues
- Enhanced button glow effects with smoother transitions
- Standardized CTA button styles across the application
- Optimized CSS loading to prevent import warnings

### Fixed
- Fixed logo display issues in light/dark mode
- Fixed navigation icons alignment
- Fixed button hover effects not working properly
- Fixed glow effects not displaying correctly
- Fixed CSS import order causing PostCSS warnings
- Fixed inconsistent button styling across the application

### Removed
- Removed redundant CSS files in favor of the new ITCSS structure
- Removed inline styles from logo container
- Removed unnecessary legacy CSS classes

## [3.3.3] - 2024-05-14

### Fixed
- Resolved critical issue where no CSS was emitted in production builds by fully resetting Astro/Vite config to defaults and removing all custom asset exclusion and plugin logic.
- Verified CSS output and link tags are present in build output; added diagnostic CSS to confirm emission.

### Changed
- Updated troubleshooting documentation and code comments for future reference.

### Upcoming
- Preparing to fix DealCard staff avatar alignment and sizing to match design reference (overlapping, circular, correct size, border).

---

## [3.3.2] - 2024-08-13

### Removed
- Removed all minification and optimization scripts that were causing build issues
- Removed unused UI components to clean up the codebase
- Removed vite-optimize-js.js plugin and its references in astro.config.mjs

### Fixed
- Fixed build process by removing problematic optimization scripts
- Fixed development environment to work without minification
- Restored components that were still being used (slider, countdown-timer, DealCardSimple)
- Fixed astro.config.mjs to remove references to optimization plugins

### Changed
- Simplified build scripts in package.json
- Organized unused files by moving them to recycle-bin instead of deleting them
- Improved project structure with cleaner organization

## [3.3.1] - 2024-08-08

### Fixed
- Fixed Supabase auth-js module errors by adding missing exports
- Improved popup positioning to appear at the top of the viewport
- Enhanced click tracking with better error handling and logging
- Fixed impression tracking to properly record impressions
- Reduced "Shop at merchant name" button size in deal popup
- Updated footer logo with new SVG files to fix loading issues

## [3.3.0] - 2024-08-07

### Added
- Created CSS optimization scripts for production builds
- Added font loading consistency improvements across all pages
- Implemented robust environment variable handling for server-side code
- Created API-specific Supabase client for better error handling
- Added simplified email templates for better deliverability

### Fixed
- Fixed Supabase URL error in newsletter API endpoints
- Fixed font loading issues in modals, popups, and specific pages
- Fixed environment variable access in server-side code
- Improved email templates with better alignment and simpler design
- Enhanced error handling in API endpoints

### Changed
- Updated build process to include CSS optimization steps
- Simplified email templates to focus on content without images
- Enhanced performance improvement plan documentation
- Updated package.json with new optimization scripts

## [3.2.0] - 2024-08-06

### Added
- Added responsive design improvements for mobile devices across all pages
- Implemented more compact filter panel with better space utilization
- Added visual grouping to filter sections with background colors and rounded corners

### Changed
- Redesigned filter panel header to be more compact and properly align "Filters" text with close button
- Improved ViewToggle component with smaller icons on mobile
- Enhanced brand, merchant, and category detail pages with separate mobile and desktop layouts
- Optimized newsletter input field to match button height on mobile devices
- Reduced padding and margins in filter panel for better space efficiency
- Made filter panel headings smaller and more compact

### Fixed
- Fixed alignment issues in filter panel header
- Fixed mobile view of brand/merchant/category detail pages with better ordering of elements
- Fixed newsletter input field height to match button height
- Fixed filter panel Apply button visibility on mobile
- Fixed close button visibility in filter panel
- Improved spacing and alignment throughout mobile views

## [3.1.0] - 2024-08-05

### Added
- Comprehensive testing framework with Playwright for end-to-end tests
- Unit tests for utility functions using Vitest
- CI/CD pipeline with GitHub Actions for automated testing and deployment
- Production build script to clean test files and documentation from production builds
- Automated production deployment workflow

### Changed
- Updated production preparation plan to mark testing implementation as complete
- Improved build process with optimized CSS and image processing
- Enhanced security measures for production deployment

### Fixed
- Fixed issues with CSS testing
- Resolved conflicts in test environment setup

## [3.0.0] - 2024-08-05

### Removed
- Removed all admin-related files and folders to simplify the codebase
- Removed admin components from `src/components/admin`
- Removed admin pages from `src/pages/admin`
- Removed admin API endpoints from `src/pages/admin/api`
- Removed admin tests from `src/tests/admin`
- Removed admin documentation files
- Updated package.json to remove admin-related scripts

### Changed
- Simplified project structure by removing admin functionality
- Improved organization by ensuring all code is properly contained within appropriate folders

### Fixed
- Fixed issues with admin components causing errors in the codebase
- Removed duplicate components that were causing confusion

## [2.9.0] - 2024-08-03

### Added
- Created comprehensive production preparation plan with detailed tasks
- Implemented Content Security Policy (CSP) for enhanced security
- Created responsive image component with WebP support and fallbacks
- Added image processing script for generating multiple sizes and formats
- Created OG image for social sharing with brand colors and logo
- Added schema.org markup for deals page with CollectionPage and Offer schemas
- Created detailed SEO optimization documentation for page titles and descriptions
- Added Lighthouse audit report with performance recommendations

### Changed
- Updated security headers in Astro configuration for better protection
- Improved DealCard component to use responsive images
- Enhanced meta tags for better social sharing
- Updated PRODUCTION_DEPLOYMENT.md with new version information
- Fixed ResponsiveImage component to work properly with server-side rendering

### Fixed
- Fixed "window is not defined" error in ResponsiveImage component
- Fixed blinking images issue in DealCard component
- Fixed duplicate content issues with canonical URLs
- Improved heading structure for better accessibility and SEO

## [2.8.0] - 2024-07-29

### Added
- Created beautiful team section on About page with staff images and structured data
- Added trusted merchants section to Contact page with text-based merchant display
- Implemented email functionality with Resend for contact form submissions
- Added automated email <NAME_EMAIL> for contact form submissions
- Added confirmation emails to users who submit contact forms
- Added quick links section to Contact page (FAQ, How It Works, About)

### Changed
- Redesigned Contact page with improved layout and partnership focus
- Updated team member images to be smaller and more consistent
- Changed CEO title to Head of Operations as requested
- Fixed arrow visibility in light mode for buttons
- Updated API endpoint for contact form to use Resend email service

### Fixed
- Fixed database query errors related to merchant image paths
- Protected email addresses on contact page with obfuscation
- Fixed button styling for better visibility in light mode
- Updated structured data for better SEO

## [2.7.0] - 2024-07-28

### Added
- Created custom 404 page with monitoring system
- Added 404 monitoring dashboard in admin section
- Updated contact page with Cloudflare Turnstile captcha
- Enhanced hero section with 3D rotating deal cards
- Created image descriptions for How It Works page
- Updated favicon with VapeHybrid branding
- Added rate limiting to sensitive API endpoints

### Fixed
- Fixed font preload issues to resolve console warnings
- Protected email address on contact page for better security
- Updated documentation with progress on pre-production tasks

## [2.6.0] - 2024-07-28

### Added
- Created comprehensive production preparation plan with detailed tasks
- Added pre-production visual audit document to identify issues
- Created placeholder blog page with "Coming Soon" message
- Added Open Graph and Twitter card meta tags for better social sharing
- Added security headers to Astro configuration (CSP, X-Frame-Options, etc.)
- Created TODO document for OG image creation

### Changed
- Updated admin credentials to use environment variables for better security
- Converted images to WebP format with significant size reductions (up to 98%)
- Implemented lazy loading for images across all components
- Updated sitemap.xml to remove authentication and account pages
- Fixed mobile navigation in admin section

### Removed
- Moved test pages to recycle bin (design-system.astro, component-comparison.astro)
- Removed test API endpoints (test-email.ts, test-tables.ts)

## [2.5.1] - 2024-07-26

### Added
- Restored newsletter testing pages to the admin dashboard
- Created NewsletterTestingDashboard component with links to all testing pages
- Added route for newsletter testing dashboard in AdminApp

### Fixed
- Fixed routing issues with newsletter testing pages
- Updated links in NewsletterDashboard to use correct paths
- Added proper navigation between newsletter dashboard and testing pages

## [2.5.0] - 2024-07-25

### Added
- Enhanced admin dashboard with user dropdown menu and profile display
- Added comprehensive documentation for Shadcn UI components in admin dashboard
- Implemented newsletter system integration documentation with roadmap
- Added token expiration handling with automatic redirection to login page

### Changed
- Updated admin dashboard to use real data from Supabase instead of mock data
- Improved authentication with token expiration (24 hours) for better security
- Enhanced error handling for API requests with better logging
- Updated API endpoints to fetch data directly from Supabase tables

### Fixed
- Fixed authentication issues in admin dashboard with proper token validation
- Fixed API endpoints to use real data from Supabase database
- Improved error handling in API requests with better error messages
- Enhanced security with proper token expiration and validation

## [2.4.0] - 2024-07-22

### Added
- Implemented email frequency options for newsletter preferences (weekly, twice weekly, biweekly, monthly)
- Added clear success message for preference updates
- Created comprehensive admin section for newsletter management
- Added detailed documentation for the newsletter system

### Changed
- Consolidated redundant newsletter database tables into a single preferences table
- Moved all newsletter testing pages to a dedicated admin section
- Enhanced preference management UI with better organization and feedback
- Improved email delivery with better error handling and logging

### Fixed
- Fixed unsubscribe link in preferences page
- Fixed preference update feedback to show clear success message
- Fixed email frequency selection to properly save to database
- Improved error handling in newsletter API endpoints

## [2.3.1] - 2024-07-20

### Fixed
- Fixed brand logo fallback in DealCard and DealListCard components when deal images are missing
- Improved image fallback logic to properly check both nested and direct properties
- Enhanced error handling for image loading with better fallback cascade
- Updated "Verified" and success rate badge text colors to use primary color (blue in light mode, green in dark mode)
- Fixed fallback text display to check for both nested and direct name properties

## [2.3.0] - 2024-07-19

### Added
- Created DealCouponModal component for consistent coupon display across the site
- Implemented DealsPageWrapper component to handle data transformation and display logic
- Added merchant name shortening functionality (e.g., "Vapesourcing" instead of "Vapesourcing Electronics Co.,Ltd.")
- Implemented improved coupon reveal functionality with proper affiliate link handling

### Changed
- Redesigned deal detail page with improved price calculation and display
- Updated price calculation logic to correctly show original price, discount, and final price
- Enhanced coupon code section with better reveal functionality and user experience
- Improved UI with consistent 25px border radius across all components
- Added pointer-events-none to background patterns to ensure clickable elements work properly

### Fixed
- Fixed merchant name display in deal cards and coupon modal
- Fixed coupon code reveal functionality to properly handle affiliate links
- Fixed price calculation to correctly derive original price from discounted price
- Fixed clickable elements being blocked by background pattern divs
- Improved button styling and interaction in deal detail page

## [2.2.0] - 2024-07-17

### Added
- Created dedicated DealListCard component in DesignSystem for list view
- Added eye icon next to heart icon for viewing deal details
- Implemented view mode persistence in URL parameters

### Changed
- Improved "Copy Code" button styling with border and hover effects
- Enhanced coupon code display with border-radius of 25px
- Reorganized deal card layout with coupon code at top right
- Updated hover states to ensure text is black on primary background in both light and dark modes
- Reduced padding on list cards for more compact appearance

### Fixed
- Fixed view mode persistence when opening and closing popups
- Fixed text visibility in dark mode for "Copy Code" buttons
- Improved URL handling to maintain view mode across page interactions

## [2.1.0] - 2024-07-15

### Added
- Implemented dynamic staff avatars with proper image paths and fallback initials
- Added success rate badge next to verified badge showing deal success percentage
- Added verification information in deal popup with date display
- Added heart icon (bookmark) and eye icon (view deal) to deal popup
- Created utility functions for generating realistic usage information and random staff avatars
- Added colored voting buttons (blue for thumbs up, red for thumbs down)

### Changed
- Updated deal cards to show "Verified X ago by Y staffers" instead of "Worked X ago for Y shoppers"
- Improved coupon section in popup with black background and green text
- Enhanced popup layout with better organization and spacing
- Reduced padding in deal cards for more compact appearance
- Updated CSS variables with proper card and border colors
- Removed toast notifications for heart icon and voting to prevent duplicates

### Fixed
- Fixed avatar display to match the exact number mentioned in the text
- Fixed image paths to use the correct staff images from public folder
- Fixed heart icon to fill with red color when clicked (not just border)
- Fixed popup styling with proper 25px border radius
- Fixed CSS variable definitions for consistent styling

## [2.0.0] - 2024-07-10

### Changed
- Completely redesigned the UI/UX of the brands page with improved layout and visual appeal
- Enhanced the DealPage component with a more spacious 1080px width layout and better balance
- Implemented glass/backdrop blur effects with patterns visible in both light and dark modes
- Improved the tabs implementation using shadcn components with better visibility and interaction
- Added subtle hover effects to all interactive elements for better user experience
- Enhanced typography with better contrast in both light and dark modes
- Redesigned related deals section with improved card layout and visual hierarchy
- Improved spacing throughout all pages for a less cramped appearance
- Balanced left and right content sections for better visual harmony

### Fixed
- Fixed visibility issues with text in dark mode across all pages
- Improved mobile responsiveness with better spacing and layout
- Enhanced accessibility with better contrast and focus states

## [1.5.1] - 2024-07-08

### Added
- Added heart icon, login icon, and logout icon to the home page navigation in EnhancedHero component
- Implemented real-time bookmarks count display with badge in navigation
- Added tooltips to all navigation icons for better usability

### Changed
- Created compact account pages with consistent design across all sections
- Redesigned account layout with smaller text and reduced spacing
- Improved mobile navigation with better organization of account-related links
- Enhanced authentication state management in the home page navigation

### Removed
- Removed obsolete account-related components and pages
- Eliminated duplicate form components in favor of compact versions

## [1.5.0] - 2024-07-05

### Added
- Implemented new Supabase SSR authentication using PKCE-based, cookie-backed approach
- Added comprehensive documentation for Supabase authentication in `@docs/setting-supabase-astro.md`
- Created new auth components: AuthProvider, AuthGuard, AuthFormWrapper
- Added email preferences management for newsletter subscriptions
- Implemented proper Row Level Security (RLS) for user-related tables
- Added new API endpoints for authentication flows (signin, signout, reset-password, etc.)

### Changed
- Reorganized documentation into structured directories (`docs/` and `@docs/`)
- Consolidated CSS architecture with improved design tokens system
- Updated auth forms with better validation and error handling
- Improved middleware for better session management
- Enhanced profile management with email preferences

### Removed
- Cleaned up obsolete authentication code and components
- Removed old documentation files from `docments/` directory
- Eliminated redundant auth endpoints and components

## [1.4.2] - 2024-06-18

### Changed
- Updated footer layout to improve organization and accessibility
- Moved legal links (Terms, Privacy, Legal) to the bottom footer
- Added commission disclosure text next to copyright in footer
- Reorganized footer links with About Us and Contact Us in the right column
- Standardized heading sizes across all homepage sections to 34px
- Improved subtitle text consistency with 18px size where appropriate
- Updated FAQ section with better styling and internal linking
- Enhanced testimonials section to display 3 cards at a time

### Fixed
- Fixed JSX syntax issues in testimonials and featured deals components
- Removed non-functioning search bar from FAQ sections
- Added proper anchor links in FAQ related questions
- Fixed rounded corners on buttons for consistent styling

## [1.4.1] - 2024-06-15

### Removed
- Removed experimental A/B testing homepage (/home) and its components
- Removed unused Aurora Background components and demo page
- Cleaned up codebase by removing test components that aren't used by index.astro

### Fixed
- Fixed featured deals query to properly filter by is_featured=true
- Improved image fallback logic to prioritize imagebig_url then brand_logo_url
- Fixed deal links to properly route to /deal/id for view and /go/id for affiliate links
- Enhanced hero section with better image display and text layout

## [1.4.0] - 2024-06-10

### Added
- Completely redesigned homepage with full-width/height hero section
- Added transparent navbar that appears on top of the hero section
- Implemented glass effects with backdrop blur for a modern look
- Added "Why Trust Us" section highlighting VapeHybrid's expertise
- Added brands carousel showcasing partner brands
- Added comparison table comparing VapeHybrid to other reviewers
- Added FAQ section answering common questions
- Created custom HomeLayout that doesn't include the default navbar

### Changed
- Improved overall design with more white space and cleaner typography
- Enhanced featured deals section with better spacing and card design
- Updated category grid with cleaner design and better spacing
- Improved newsletter and testimonials sections to match the new aesthetic
- Made subtitle text smaller and cleaner for better readability
- Updated button styling with dark text for better visibility
- Properly used imagebig_url from the deals table for better image quality

### Fixed
- Fixed image loading with proper error handling for missing images
- Improved performance with lazy loading and optimized animations
- Enhanced mobile responsiveness for all new sections

## [1.3.3] - 2024-06-01

### Added
- Created comprehensive legal and compliance pages (Terms & Conditions, Privacy Policy, Affiliate Disclosure, Disclaimer, Contact Us, About Us, Cookie Policy)
- Added Legal Hub page as a central location for all legal documents
- Implemented FAQ component for the deals page with structured data for SEO
- Added contact form with Supabase integration

### Changed
- Improved footer design with better organization and visual appeal
- Updated footer to include logo and more structured content sections
- Replaced border with full-width divider in footer for better visual consistency
- Reduced font size for copyright and disclosure text for cleaner appearance

### Fixed
- Enhanced typography and spacing in legal pages for better readability
- Improved list styling with proper indentation and bullet points
- Added proper margins and padding for headings, paragraphs, and lists
- Fixed scrollbar styling for better user experience

## [1.3.2] - 2024-05-25

### Added
- Reimplemented bookmarks functionality with improved database integration
- Added heart icon next to View Deal button for better visibility
- Added bookmark counter in navigation bar showing number of saved items

### Fixed
- Fixed bookmarks page to fetch data directly from database instead of localStorage
- Fixed button styling in bookmarks table for better visibility in dark/light modes
- Fixed image loading in bookmarks table with proper fallback handling
- Fixed calculation logic for discounted prices and savings
- Fixed API endpoint to handle bookmark IDs correctly

### Changed
- Updated BookmarkButton to store only deal IDs instead of complete deal data
- Improved styling for savings text with better color contrast
- Enhanced button styling in bookmarks table with proper background colors
- Updated API endpoint to fetch only existing database columns

## [1.3.1] - 2024-05-23

### Added
- Implemented "Remember Me" functionality for authentication with 30-day session option
- Added elegant scrollbar styling across the entire website with theme-aware colors
- Added screen reader descriptions to improve accessibility for dialog components

### Fixed
- Fixed accessibility warnings for DialogContent and SheetContent components
- Fixed layout shift when switching between sign-in and sign-up tabs
- Updated checkbox styling to use Tetradic 1 color for better visibility
- Fixed scrollbar appearance in auth modal and filter drawer

### Changed
- Simplified welcome text in authentication modal
- Made "Remember me" text smaller and more subtle
- Enhanced scrollbar styling with primary color for light theme and secondary color for dark theme

## [1.3.0] - 2024-05-10

### Removed
- Completely removed saved deals functionality due to persistence and synchronization issues
- Removed saved deals context provider and related components
- Removed saved deals page and route
- Removed save button from deal cards
- Removed saved deals links from navigation and user menu

### Changed
- Updated auth components to remove saved deals functionality
- Improved UI components to work without saved deals feature
- Enhanced documentation with removal details and future plans
- Updated navigation components to remove saved deals references

### Added
- Added comprehensive documentation for the removal process
- Created detailed plan for reimplementing saved deals functionality
- Added changelog updates for better version tracking

## [1.2.0] - 2024-04-21

### Added
- Deal validity rating system with success rate calculation
- "Verified by VapeHybrid" badge for deals with high success rates
- User voting system (thumbs up/down) for deals
- Affiliate click tracking with click count
- Impression tracking for deals
- Fallback redirects for affiliate links
- Row Level Security (RLS) for all tables
- Security improvements for database functions

### Changed
- Updated DealCard and DealCardSimple components to display success rate and verification badge
- Improved error handling in API endpoints
- Enhanced security with proper RLS policies
- Fixed trigger functions to handle all operations correctly

### Fixed
- Fixed ambiguous column reference in SQL functions
- Fixed RLS policies to allow anonymous users to vote and track impressions
- Fixed click tracking to properly increment click_count
- Fixed verification badge display on deal pages

## [1.1.3] - 2024-07-02

### Added
- Created categories page with grid layout and alphabetical sorting
- Created category detail page with sidebar and deals listing
- Created merchants page with alphabetical navigation
- Created merchant detail page with logo, website link, and deals
- Created brands page with featured brands section
- Created brand detail page with stats and merchant listings
- Created search page with global search across all entities
- Added structured data for SEO on all pages

### Fixed
- Fixed ViewToggle and SortSelect components to work without URL parameters
- Fixed image fallback logic to properly cascade through all available images
- Fixed pagination component to handle URL parameters correctly
- Fixed "Reveal Code" button text color in dark mode for better readability
- Set list view as default for brand, category, and merchant detail pages

### Changed
- Swapped primary and secondary colors in dark mode for better contrast
- Updated dark mode text colors on primary backgrounds to be black for better readability
- Improved error handling in components
- Enhanced URL parameter handling for better navigation

## [1.1.2] - 2024-07-01

### Fixed
- Fixed discount filtering to use numeric column instead of text-based filtering
- Fixed copy and voting buttons to only work after revealing the code
- Fixed background color in dark mode for deal detail page
- Fixed the track-click API to directly update click_count without using RPC
- Fixed voting functionality to only allow one vote per deal
- Improved mobile responsiveness for list view with larger images and text
- Enhanced the reveal code functionality to prioritize affiliate link visits
- Fixed "Go to Deal" button on deal detail page, replaced with "Back to All Deals"

### Changed
- Updated the color scheme to use #262cbd as primary color
- Changed dark mode background to #0a0a0a
- Improved the DealCard component with better UI/UX
- Enhanced the two-step process for revealing codes: first visit affiliate link, then reveal code
- Simplified API calls to improve performance and reliability

### Security
- Improved security by removing sensitive information from console logs

## [1.1.1] - 2025-04-19

### Fixed
- Fixed interactive buttons (copy, save, vote) functionality in DealCard component
- Improved reveal code functionality to immediately redirect to affiliate links
- Fixed persistence of revealed codes when returning to the deals page
- Updated API endpoints to handle different parameter naming conventions
- Fixed image loading with eager loading for better performance
- Removed Supabase key exposure in console logs
- Fixed clicks table structure to match database schema

### Changed
- Refactored DealCard component to use React with client-side hydration
- Improved error handling in API endpoints
- Enhanced cookie and localStorage handling for better state persistence
- Updated API response formats for better compatibility

## [1.1.0] - 2025-04-18

### Added
- Implemented filtering system with price and discount range sliders
- Added sorting options (newest, price low/high, discount high/low, expiring soon, most popular)
- Created pagination system with per-page selection
- Added image fallback hierarchy (deal image → brand logo → merchant logo → category logo → placeholder)
- Implemented clickable images that route through affiliate links
- Added mobile filter panel with improved UI

### Fixed
- Fixed discount filtering to properly handle percentage values
- Resolved issues with price filtering and sorting
- Fixed mobile filter button functionality
- Improved image display with object-contain for better aspect ratios

### Changed
- Replaced infinite scroll with traditional pagination for better reliability
- Converted React pagination components to Astro components for better server-side rendering
- Improved filter UI with better mobile responsiveness
- Enhanced deal count display to show total matching deals

### Technical Details
- Implemented proper SQL-based filtering for discount percentages
- Created separate count query to get accurate total deal counts
- Improved URL parameter handling for filters, sorting, and pagination
- Removed duplicate and conflicting component files

## [1.0.0] - 2025-04-17

### Added
- Initial release of the VapeHybrid Coupon Site
- Core functionality: reveal code, copy, save, vote
- Implemented Astro with React components
- Added Tailwind CSS and ShadCN UI for styling
- Integrated Supabase for database and authentication
- Created API endpoints for deals, merchants, categories, etc.
- Implemented coupon reveal functionality with click tracking
- Added user authentication for saved deals
- Implemented voting system (thumbs up/down)
- Created responsive design with grid and list views
- Added visual feedback for user actions (copy, save, vote)
- Created comprehensive documentation

### Fixed
- Fixed Supabase client initialization issues
- Resolved hydration errors in components
- Fixed cookie handling for revealed deals
- Improved error handling for API endpoints
- Added fallback handling for missing database tables

### Changed
- Updated Astro configuration to use server-side rendering
- Improved DealCard component with better UX
- Enhanced error messages and logging

### Technical Details
- Set up Astro with Node.js adapter for server-side rendering
- Created database schema for deals, clicks, votes, and saved deals
- Implemented API endpoints with proper error handling
- Added environment variable handling for Supabase credentials
- Created SQL migration scripts for database setup
