/**
 * OptimizedDealListCard Component
 *
 * An enhanced and optimized version of the DealListCard component with better performance,
 * accessibility, and user experience.
 */

import React, { useState, useRef, useMemo, useCallback, useEffect } from 'react';
import { cn } from '../../../lib/utils';
import Card from './Card';
import { ThumbsUp, Eye } from 'lucide-react';
import { getRandomStaffImages, generateUsageInfo } from '../../../utils/dealUtils';
import BookmarkButton from '../../BookmarkButton';
import { DealImage } from '../DealImage';
import DealStructuredData from '../SEO/DealStructuredData';
import { copyToClipboard } from '../../../utils/clipboardUtils';
import { generateCouponUrl } from '../../../utils/couponUrlUtils';
import { normalizeUrl } from '../../../utils/urlUtils';
import usePrefetch from '../../../hooks/usePrefetch';
import useEngagementTracking from '../../../hooks/useEngagementTracking';
import type { DealCardProps } from '../../../types/deal';

// Extended props interface to include onShowCouponModal
interface ExtendedDealListCardProps extends DealCardProps {
  onShowCouponModal?: (deal: any) => void;
}

// Create a delayed version of DealStructuredData using requestIdleCallback
const DelayedStructuredData = ({ deal, imageUrl }: { deal: any, imageUrl: string }) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    // Use requestIdleCallback to delay rendering until the browser is idle
    const requestIdleCallbackPolyfill =
      window.requestIdleCallback ||
      ((cb) => setTimeout(cb, 1));

    const idleCallbackId = requestIdleCallbackPolyfill(() => {
      setShouldRender(true);
    });

    return () => {
      // Use cancelIdleCallback to clean up
      const cancelIdleCallbackPolyfill =
        window.cancelIdleCallback ||
        ((id) => clearTimeout(id));

      cancelIdleCallbackPolyfill(idleCallbackId);
    };
  }, []);

  if (!shouldRender) return null;

  return <DealStructuredData deal={deal} imageUrl={imageUrl} />;
};

/**
 * OptimizedDealListCard - A fully optimized version of the DealListCard component
 *
 * Features:
 * - Improved performance with memoization and optimized rendering
 * - Enhanced accessibility with proper ARIA attributes
 * - Better image loading with priority hints
 * - Structured data for SEO
 * - Viewport tracking for analytics
 * - Modern clipboard API with fallbacks
 * - Improved visual feedback and animations
 */
export const OptimizedDealListCard: React.FC<ExtendedDealListCardProps> = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  onShowCouponModal,
  ...props
}) => {
  // State for code revealing
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);

  // Create refs for the card element
  const cardRef = useRef<HTMLDivElement>(null);

  // Set up prefetching for deal links
  const dealUrl = `/deal/${deal.id}`;
  const { onMouseEnter: handlePrefetch, onMouseLeave: handleCancelPrefetch } = usePrefetch(dealUrl, { delay: 150 });

  // Set up engagement tracking
  const {
    setIsVisible,
    handleMouseEnter: trackMouseEnter,
    handleMouseLeave: trackMouseLeave,
    handleClick: trackClick
  } = useEngagementTracking(deal.id, 'deal', { minDwellTime: 2000 });

  // Memoize computed values to prevent recalculation on every render
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = useMemo(() => usageInfo.timeAgo, [usageInfo]);
  const count = useMemo(() => deal.usage_count || usageInfo.count, [deal.usage_count, usageInfo.count]);

  // Calculate days remaining until expiration
  const daysRemaining = useMemo(() => {
    if (!deal.deal_end_date) return null;
    return Math.ceil((new Date(deal.deal_end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
  }, [deal.deal_end_date]);

  // Determine if the deal is expiring soon or expired
  const isExpiringSoon = useMemo(() => {
    return daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  }, [daysRemaining]);

  const isExpired = useMemo(() => {
    return daysRemaining !== null && daysRemaining <= 0;
  }, [daysRemaining]);

  // Get the appropriate image URL with fallbacks
  const imageUrl = useMemo(() => {
    // 1. First try to get the deal image
    const dealImage = normalizeUrl(deal.imagebig_url) ||
                      normalizeUrl(deal.image_url) ||
                      normalizeUrl(deal.imagesmall_url);

    if (dealImage) return dealImage;

    // 2. Try brand logos next
    const brandLogo = normalizeUrl(deal.brands?.logo_url) ||
                      normalizeUrl(deal.brand_logo_url);

    if (brandLogo) return brandLogo;

    // 3. Try merchant logos next
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) ||
                         normalizeUrl(deal.merchant_logo_url);

    if (merchantLogo) return merchantLogo;

    // 4. Use SVG as the default fallback
    return '/placeholder-image.svg';
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);

  // Track when the card becomes visible in the viewport
  useEffect(() => {
    if (!cardRef.current) return;

    // Create an observer to track when the card becomes visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          // Update visibility state for engagement tracking
          setIsVisible(entry.isIntersecting);

          if (entry.isIntersecting) {
            // Only track in production
            if (process.env.NODE_ENV !== 'production') {
              console.log('Deal impression tracked:', deal.id);
            } else {
              // Track the impression using Beacon API if available
              try {
                // Generate or get a session ID
                let sessionId = localStorage.getItem('vh_session_id');
                if (!sessionId) {
                  sessionId = 'session_' + Math.random().toString(36).substring(2, 15);
                  localStorage.setItem('vh_session_id', sessionId);
                }

                if (typeof navigator.sendBeacon === 'function') {
                  // Use the Beacon API for more reliable tracking
                  const trackingData = new FormData();
                  trackingData.append('deal_id', deal.id.toString());
                  trackingData.append('session_id', sessionId);
                  navigator.sendBeacon('/api/track-impression?deal_id=' + deal.id + '&session_id=' + sessionId, trackingData);
                } else {
                  // Fallback to fetch with keepalive
                  fetch('/api/track-impression?deal_id=' + deal.id + '&session_id=' + sessionId, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deal_id: deal.id, session_id: sessionId }),
                    keepalive: true
                  });
                }
              } catch (error) {
                console.error('Error tracking impression:', error);
              }
            }
          }
        });
      },
      { threshold: 0.5 } // Card is considered visible when 50% is in view
    );

    // Start observing the card
    observer.observe(cardRef.current);

    // Clean up the observer when the component unmounts
    return () => {
      observer.disconnect();
    };
  }, [deal.id, cardRef, setIsVisible]);

  // Handle card click - reveals code, opens popup, and navigates to affiliate link
  const handleDealClick = useCallback(() => {
    // Mark this card as revealed
    setIsCodeRevealed(true);

    if (typeof window !== 'undefined') {
      // Save to localStorage that this code has been revealed
      const revealedCodes = JSON.parse(localStorage.getItem('revealedCodes') || '{}');
      revealedCodes[deal.id] = true;
      localStorage.setItem('revealedCodes', JSON.stringify(revealedCodes));

      // Copy the code to clipboard
      if (deal.coupon_code) {
        copyToClipboard(deal.coupon_code);
      }

      // Preserve the current view mode in the URL
      const currentUrl = new URL(window.location.href);
      const viewMode = currentUrl.searchParams.get('view') || 'list';

      // Create a new URL for the popup
      const popupUrl = new URL(window.location.href);
      popupUrl.searchParams.set('dealId', deal.id.toString());
      popupUrl.searchParams.set('showPopup', 'true');
      popupUrl.searchParams.set('view', viewMode); // Preserve the view mode

      // Open popup in new tab
      window.open(popupUrl.toString(), '_blank');

      // Call the parent component's showCouponModal function to show the popup
      if (onShowCouponModal) {
        onShowCouponModal(deal);
      }

      // Navigate to the tracking URL in the current window
      // This should be the last action as it navigates away
      if (deal.tracking_url) {
        window.location.href = deal.tracking_url;
      } else {
        // If no tracking URL, use the /go/id format
        window.location.href = `/go/${deal.id}`;
      }
    }
  }, [deal, onShowCouponModal]);

  // Handle copy button click - reveals code, copies to clipboard, opens popup, and navigates
  const handleCopy = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent default card click behavior

    // Track the click for engagement metrics
    trackClick();

    // Use requestAnimationFrame to batch DOM updates and prevent forced reflow
    requestAnimationFrame(() => {
      // Reveal the code
      setIsCodeRevealed(true);

      if (typeof window !== 'undefined') {
        // Send tracking beacon for this specific deal only
        if (typeof navigator.sendBeacon === 'function') {
          try {
            const trackingData = new FormData();
            trackingData.append('deal_id', deal.id.toString());
            trackingData.append('fallback', 'true');
            navigator.sendBeacon('/api/track-click', trackingData);
            console.log(`Tracking copy for deal_id=${deal.id} only`);
          } catch (error) {
            console.error('Error sending beacon:', error);
          }
        }

        // Mark this deal as clicked
        const clickedDeals = JSON.parse(localStorage.getItem('clickedDeals') || '{}');

        // Save to localStorage that this code has been revealed
        const revealedCodes = JSON.parse(localStorage.getItem('revealedCodes') || '{}');
        revealedCodes[deal.id] = true;
        localStorage.setItem('revealedCodes', JSON.stringify(revealedCodes));

        // Mark this deal as clicked for future reference
        clickedDeals[deal.id] = true;
        localStorage.setItem('clickedDeals', JSON.stringify(clickedDeals));

        // Copy the code to clipboard
        if (deal.coupon_code) {
          copyToClipboard(deal.coupon_code);
        }

        // Preserve the current view mode in the URL
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get('view') || 'list';

        // Create a new URL for the popup - using the current page URL
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set('dealId', deal.id.toString());
        popupUrl.searchParams.set('showPopup', 'true');
        popupUrl.searchParams.set('solidBg', 'true'); // Add solid background
        popupUrl.searchParams.set('view', viewMode); // Preserve the view mode

        // Call the parent component's showCouponModal function to show the popup
        if (onShowCouponModal) {
          onShowCouponModal(deal);
        }

        // IMPORTANT: First open a new tab with the popup URL
        window.open(popupUrl.toString(), '_blank');

        // THEN navigate to the tracking URL in the current window
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          // If no tracking URL, use the /go/id format
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, trackClick, onShowCouponModal]);

  return (
    <>
      {/* Add structured data for SEO - Delayed with requestIdleCallback for better FCP */}
      {typeof window !== 'undefined' && (
        <DelayedStructuredData deal={deal} imageUrl={imageUrl} />
      )}

      <Card
        ref={cardRef}
        variant="default"
        interactive={true}
        glowEffect={true}
        className={cn(
          'optimized-deal-list-card deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between',
          'transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.005]',
          'hover:border-design-primary/50 dark:hover:border-design-primary/50',
          'border-black/15 dark:border-white/15', // Distinguishable border in both modes
          'optimized-card', // Add content-visibility optimization
          className
        )}
        style={{
          padding: '0.5rem',
          borderRadius: '25px',
          borderWidth: '1.5px'
        }}
        onClick={() => {
          handleDealClick();
          trackClick();
        }}
        onMouseEnter={() => {
          handlePrefetch();
          trackMouseEnter();
        }}
        onMouseLeave={() => {
          handleCancelPrefetch();
          trackMouseLeave();
        }}
        aria-label={`Deal: ${deal.title}`}
        role="button"
        tabIndex={0}
        {...props}
      >
        {/* Anti-Bounce Link Fallback - We'll handle this in the click handler instead */}
        <div
          id={`tracking-pixel-${deal.id}`}
          style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
          aria-hidden="true"
        />

        {/* Expiration Badge (if applicable) */}
        {isExpiringSoon && (
          <div className="absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full">
            {daysRemaining === 1 ? 'Expires today' : `Expires in ${daysRemaining} days`}
          </div>
        )}
        {isExpired && (
          <div className="absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full">
            Expired
          </div>
        )}

        {/* Image */}
        <div className="w-full sm:w-[200px] py-2 sm:py-0">
          <div className="flex justify-center h-full">
            <div
              className="relative flex items-center justify-center w-full h-full aspect-square max-w-[160px] sm:min-w-[160px] sm:max-w-[180px] sm:max-h-[180px] bg-white dark:bg-white rounded-lg overflow-hidden"
              style={{
                boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.05)',
                padding: '8px' // Add padding to ensure image doesn't touch the edges
              }}
            >
              {/* Enhanced Deal Image Component */}
              <DealImage
                src={imageUrl}
                alt={deal.title || 'Deal image'}
                width={180}
                height={180}
                priority={priority}
                fetchpriority={priority ? 'high' : 'auto'}
                className=""
                fallbackSrc="/placeholder-image.svg"
                index={0}
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-3 w-full">
          <div className="h-full flex flex-col justify-between">
            {/* Discount and Coupon Code - Always at the top for prominence */}
            <div className="mb-2">
              <div className="text-xl font-bold text-design-foreground">
                {deal.discount ?
                  <span className="text-green-600 dark:text-green-500">
                    {deal.discount}% <span className='text-xs font-normal text-gray-500 dark:text-gray-400'>off </span>🔥
                  </span> :
                  deal.price ?
                    `${deal.currency || '$'}${deal.price}` :
                    '20% Off'
                }

                {/* Urgency indicator */}
                {daysRemaining !== null && daysRemaining > 0 && (
                  <span className="ml-2 text-xs font-normal text-design-warning animate-pulse">
                    {daysRemaining > 365 ? 
                      `${Math.floor(daysRemaining / 365)}+ ${Math.floor(daysRemaining / 365) === 1 ? 'year' : 'years'} left` : 
                      daysRemaining > 30 ? 
                        `${Math.floor(daysRemaining / 30)} ${Math.floor(daysRemaining / 30) === 1 ? 'month' : 'months'} left` : 
                        daysRemaining === 1 ? 'Ends today!' : `Ends in ${daysRemaining} days!`
                    }
                  </span>
                )}
              </div>
            </div>

            {/* Brand/Merchant Name */}
            <div className="text-sm text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1">
              <span
                className="font-medium truncate max-w-[120px] hover:underline cursor-help"
                title={`Brand: ${deal.merchants?.name || deal.brands?.name || 'Unknown'}`}
              >
                {deal.merchants?.name || deal.brands?.name || 'Brand'}
              </span>

              {/* Verified Badge */}
              {deal.verified && (
                <span className="c-verified-badge verified-badge ml-2">
                  <img
                    src="/Vapehybrid light icon.svg"
                    alt="Verified"
                    className="dark:hidden"
                    width={16}
                    height={16}
                  />
                  <img
                    src="/Vapehybrid dark icon.svg"
                    alt="Verified"
                    className="hidden dark:block"
                    width={16}
                    height={16}
                  />
                  <span className="text-design-primary dark:text-design-primary">Verified</span>
                </span>
              )}

              {/* Success Rate Badge */}
              {deal.success_rate !== undefined && (
                <span
                  className={`ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${
                    deal.success_rate >= 90 ? 'bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground' :
                    deal.success_rate >= 70 ? 'bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground' :
                    'bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground'
                  }`}
                  title={`${Math.round(deal.success_rate || 85)}% success rate`}
                >
                  <ThumbsUp size={10} className="mr-1" />
                  {Math.round(deal.success_rate || 85)}%
                </span>
              )}
            </div>

            {/* Title */}
            <h3
              className="deal-card-title line-clamp-2 mb-2 text-base font-semibold overflow-hidden"
              title={deal.cleaned_title && deal.cleaned_title !== 'null' ? deal.cleaned_title : deal.title}
            >
              {deal.cleaned_title && deal.cleaned_title !== 'null' ? deal.cleaned_title : deal.title}
            </h3>

            <div className="flex flex-1">
              {/* Left side content */}
              <div className="flex-1">
                {/* Description - Only show in list view */}
                {deal.description && (
                  <p
                    className="text-sm text-design-muted-foreground mb-3 line-clamp-2"
                    title={deal.description}
                  >
                    {deal.description}
                  </p>
                )}

                {/* Usage Info - Enhanced Staff Avatars */}
                <div className="flex items-center text-xs text-design-muted-foreground mb-3">
                  <div className="c-avatar-group user-avatar-group flex relative" style={{ marginRight: '0.35rem' }}>
                    {staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => (
                      <div
                        key={index}
                        className="c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden"
                        style={{
                          width: '20px',
                          height: '20px',
                          marginLeft: index > 0 ? '-8px' : '0',
                          borderRadius: '50%',
                          border: '1.5px solid white',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                          zIndex: 3 - index,
                          backgroundColor: avatar.color // Fallback background color
                        }}
                        title={`Staff member: ${avatar.name}`}
                      >
                        {/* Use picture element for WebP with PNG fallback */}
                        {avatar.webpPath && avatar.imagePath ? (
                          <picture>
                            <source srcSet={avatar.webpPath} type="image/webp" />
                            <img
                              src={avatar.imagePath}
                              alt={avatar.initials}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                // If image fails to load, show initials instead
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                // Make sure the parent div shows the fallback content
                                const parent = target.parentElement?.parentElement;
                                if (parent) {
                                  parent.classList.add('fallback-active');
                                }
                              }}
                            />
                          </picture>
                        ) : (
                          // Fallback to initials if no image path
                          <div className="w-full h-full flex items-center justify-center text-white font-bold">
                            <span style={{ fontSize: '8px' }}>{avatar.initials}</span>
                          </div>
                        )}

                        {/* Always include fallback content that will be shown if image fails */}
                        <div className="fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0">
                          <span style={{ fontSize: '8px' }}>{avatar.initials}</span>
                        </div>
                      </div>
                    ))}
                    {count > 3 && (
                      <div
                        className="c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20"
                        style={{
                          width: '20px',
                          height: '20px',
                          marginLeft: '-8px',
                          borderRadius: '50%',
                          border: '1.5px solid white',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                          fontSize: '8px'
                        }}
                        title={`${count - 3} more staff members`}
                      >
                        +{count - 3}
                      </div>
                    )}
                  </div>
                  <span>
                    Verified <time dateTime={deal.last_verified_at} title={new Date(deal.last_verified_at || '').toLocaleString()}>{usageTimeAgo}</time> by {count || 3} staffer{(count || 3) > 1 ? 's' : ''}
                  </span>
                </div>

                {/* Limited time indicator */}
                {daysRemaining !== null && daysRemaining > 0 && (
                  <div className="mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center">
                    <span className="animate-pulse mr-1">⏱</span>
                    <span>Limited time offer! {daysRemaining > 365 ? 
                      `${Math.floor(daysRemaining / 365)}+ ${Math.floor(daysRemaining / 365) === 1 ? 'year' : 'years'} left` : 
                      daysRemaining > 30 ? 
                        `${Math.floor(daysRemaining / 30)} ${Math.floor(daysRemaining / 30) === 1 ? 'month' : 'months'} left` : 
                        daysRemaining === 1 ? 'Ends today' : `Ends in ${daysRemaining} days`
                      }</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right side column */}
        <div className="flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0">
          {/* Coupon code box */}
          <div
            className="deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center bg-design-muted rounded-full border border-design-muted-foreground/10 overflow-hidden"
            onClick={(e) => {
              e.stopPropagation();
              handleCopy(e);
            }}
            title={isCodeRevealed ? 'Click to copy' : 'Click to reveal code'}
            aria-label={isCodeRevealed ? `Copy coupon code: ${deal.coupon_code || 'NO CODE'}` : 'Click to reveal coupon code'}
          >
            {deal.coupon_code ? (
              <>
                <span
                  className={`text-base font-bold transition-all duration-300 ${isCodeRevealed ? 'blur-none opacity-100' : 'blur-[4px] select-none'}`}
                  style={{ color: 'var(--design-foreground)' }}
                  aria-hidden={!isCodeRevealed}
                >
                  {deal.coupon_code}
                </span>
                {isCodeRevealed && (
                  <span
                    className="absolute inset-0 bg-design-primary/10 animate-reveal-sweep"
                    aria-hidden="true"
                  ></span>
                )}
                {/* Hidden text for screen readers */}
                <span className="sr-only">
                  {isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : 'Click to reveal coupon code'}
                </span>
              </>
            ) : (
              'NO CODE'
            )}
          </div>

          {/* Copy Code button */}
          <button
            className="copy-code-button px-4 py-1 rounded-full border border-design-muted-foreground/20 hover:border-design-primary/50 dark:hover:border-design-primary/50 transition-colors"
            onClick={(e) => {
              e.stopPropagation();
              handleCopy(e);
            }}
            aria-label={`Copy coupon code ${deal.coupon_code || 'for this deal'}`}
          >
            Copy Code
          </button>

          {/* Icons row */}
          <div className="deal-card-actions flex items-center mt-1">
            <BookmarkButton
              dealId={deal.id.toString()}
              className="bookmark-button p-2 rounded-full hover:bg-design-muted transition-colors"
            />
            <button
              className="eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10"
              onClick={(e) => {
                e.stopPropagation();
                window.open(generateCouponUrl(deal), '_blank');
              }}
              title="View coupon details"
              aria-label="View coupon details"
            >
              <Eye size={18} className="text-design-muted-foreground hover:text-design-foreground transition-colors" />
            </button>
          </div>
        </div>
      </Card>
    </>
  );
};

// Export both named and default for compatibility
export default OptimizedDealListCard;
