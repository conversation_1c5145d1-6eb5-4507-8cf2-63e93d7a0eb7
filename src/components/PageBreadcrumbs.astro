---
/**
 * PageBreadcrumbs Component
 * 
 * An enhanced breadcrumbs component for pages that don't use InfoPageHeader.
 * Shows the full page navigation path in a clean, minimal design.
 * Supports multiple navigation levels with proper hierarchical order.
 */

export interface BreadcrumbItem {
  label: string;
  href: string;
  current?: boolean;
}

export interface Props {
  /**
   * Array of breadcrumb items defining the navigation path
   */
  items?: BreadcrumbItem[];
  
  /**
   * Legacy prop for backward compatibility
   * @deprecated Use items instead
   */
  currentPage?: string;
  
  /**
   * Class to apply to the container
   */
  class?: string;
}

const { items, currentPage, class: className = '' } = Astro.props;

// For backward compatibility
let breadcrumbItems: BreadcrumbItem[] = [];

if (items && items.length > 0) {
  breadcrumbItems = items;
} else if (currentPage) {
  breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: currentPage, href: '#', current: true }
  ];
} else {
  breadcrumbItems = [
    { label: 'Home', href: '/' }
  ];
}

// Set the last item as current if not explicitly set
if (!breadcrumbItems.some(item => item.current)) {
  breadcrumbItems[breadcrumbItems.length - 1].current = true;
}
---

<nav aria-label="Breadcrumbs" class={`mb-4 ${className}`}>
  <ol class="flex flex-wrap items-center justify-center text-xs text-design-muted-foreground font-medium">
    {breadcrumbItems.map((item, index) => (
      <li class="flex items-center">
        {index > 0 && (
          <span class="mx-2 text-design-muted-foreground/60">/</span>
        )}
        {item.current ? (
          <span aria-current="page" class="text-design-foreground font-semibold">{item.label}</span>
        ) : (
          <a href={item.href} class="hover:text-primary transition-colors">{item.label}</a>
        )}
      </li>
    ))}
  </ol>
</nav>
