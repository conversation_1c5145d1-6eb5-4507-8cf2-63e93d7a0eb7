---
import MainLayout from '../../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../../utils/supabase/server.js';
import OptimizedDealsPageWrapper from '../../../components/OptimizedDealsPageWrapper';
import ViewToggle from '../../../components/DesignSystem/ViewToggle';
import SortSelect from '../../../components/DesignSystem/SortSelect';
import Pagination from '../../../components/DesignSystem/Pagination.astro';
import PerPageSelect from '../../../components/DesignSystem/PerPageSelect.astro';
import FilterDrawer from '../../../components/DesignSystem/FilterDrawer';
import { GlowingButton } from '../../../components/DesignSystem/Button/GlowingButton';
import WebPImage from '../../../components/DesignSystem/WebPImage.astro';
import UniversalEmailCaptureForm from '../../../components/shared/UniversalEmailCaptureForm';
import CategoryFAQ from '../../../components/Category/CategoryFAQ';
import CategoryActivity from '../../../components/Category/CategoryActivity';
import CategoryRelatedSections from '../../../components/Category/CategoryRelatedSections';
import ExpandableCategoryInfo from '../../../components/Category/ExpandableCategoryInfo';
import CategoryAbout from '../../../components/Category/CategoryAbout';
import { generateCategoryPageSchema } from '../../../utils/schema-markup';
import { generateCategorySEOMeta, generateMetaTagsObject, generateSocialProofText } from '../../../utils/seo-meta-generators';
import { calculateCouponStats } from '../../../utils/coupon-stats';
import { generateRecentVerifications as generateVerifications } from '../../../utils/verification-time';

// Get the category slug from the URL
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/coupons/categories', 302);
}

// Create server-side Supabase client
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

// Get query parameters
const url = new URL(Astro.request.url);
const view = url.searchParams.get('view') || 'grid';
const sort = url.searchParams.get('sort') || 'newest';
const page = parseInt(url.searchParams.get('page') || '1');
const limit = parseInt(url.searchParams.get('limit') || '24');

// Filter parameters
const merchantIds = url.searchParams.get('merchants')?.split(',').map(id => parseInt(id)).filter(id => !isNaN(id)) || [];
const brandIds = url.searchParams.get('brands')?.split(',').map(id => parseInt(id)).filter(id => !isNaN(id)) || [];
const minPrice = parseFloat(url.searchParams.get('min_price') || '0');
const maxPrice = parseFloat(url.searchParams.get('max_price') || '200');
const minDiscount = parseFloat(url.searchParams.get('min_discount') || '0');
const maxDiscount = parseFloat(url.searchParams.get('max_discount') || '100');
const expiringSoon = url.searchParams.get('expiring_soon') === 'true';
const validLongTerm = url.searchParams.get('valid_long_term') === 'true';

// Fetch category information
const { data: category, error: categoryError } = await supabase
  .from('categories')
  .select('*')
  .eq('slug', slug)
  .single();

if (categoryError || !category) {
  console.error('Category not found:', categoryError);
  return Astro.redirect('/coupons/categories', 302);
}

// Build deals query for this category with filters
let dealsQuery = supabase
  .from('deals')
  .select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `)
  .eq('category_id', category.id)
  .gt('deal_end_date', new Date().toISOString());

// Apply filters
if (merchantIds.length > 0) {
  dealsQuery = dealsQuery.in('merchant_id', merchantIds);
}

if (brandIds.length > 0) {
  dealsQuery = dealsQuery.in('brand_id', brandIds);
}

if (minPrice > 0 || maxPrice < 200) {
  dealsQuery = dealsQuery.gte('price', minPrice).lte('price', maxPrice);
}

if (minDiscount > 0 || maxDiscount < 100) {
  dealsQuery = dealsQuery.gte('discount_percentage', minDiscount).lte('discount_percentage', maxDiscount);
}

if (expiringSoon) {
  const threeDaysFromNow = new Date();
  threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
  dealsQuery = dealsQuery.lte('deal_end_date', threeDaysFromNow.toISOString());
}

if (validLongTerm) {
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
  dealsQuery = dealsQuery.gte('deal_end_date', thirtyDaysFromNow.toISOString());
}

// Apply sorting
switch (sort) {
  case 'newest':
    dealsQuery = dealsQuery.order('created_at', { ascending: false });
    break;
  case 'expiring_soon':
    dealsQuery = dealsQuery.order('deal_end_date', { ascending: true });
    break;
  case 'discount_desc':
    dealsQuery = dealsQuery.order('discount_percentage', { ascending: false, nullsLast: true });
    break;
  case 'most_popular':
    dealsQuery = dealsQuery.order('click_count', { ascending: false, nullsLast: true });
    break;
  default:
    dealsQuery = dealsQuery.order('created_at', { ascending: false });
}

// Apply pagination
const offset = (page - 1) * limit;
dealsQuery = dealsQuery.range(offset, offset + limit - 1);

// Execute deals query
const { data: deals, error: dealsError } = await dealsQuery;

// Get total count for pagination with same filters
let countQuery = supabase
  .from('deals')
  .select('*', { count: 'exact', head: true })
  .eq('category_id', category.id)
  .gt('deal_end_date', new Date().toISOString());

// Apply same filters to count query
if (merchantIds.length > 0) {
  countQuery = countQuery.in('merchant_id', merchantIds);
}

if (brandIds.length > 0) {
  countQuery = countQuery.in('brand_id', brandIds);
}

if (minPrice > 0 || maxPrice < 200) {
  countQuery = countQuery.gte('price', minPrice).lte('price', maxPrice);
}

if (minDiscount > 0 || maxDiscount < 100) {
  countQuery = countQuery.gte('discount_percentage', minDiscount).lte('discount_percentage', maxDiscount);
}

if (expiringSoon) {
  const threeDaysFromNow = new Date();
  threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
  countQuery = countQuery.lte('deal_end_date', threeDaysFromNow.toISOString());
}

if (validLongTerm) {
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
  countQuery = countQuery.gte('deal_end_date', thirtyDaysFromNow.toISOString());
}

const { count: totalCount } = await countQuery;

// Calculate pagination
const totalPages = Math.ceil((totalCount || 0) / limit);

// Get all category deals for statistics (without filters for accurate stats)
const { data: allCategoryDeals } = await supabase
  .from('deals')
  .select('*')
  .eq('category_id', category.id)
  .gt('deal_end_date', new Date().toISOString());

// Calculate enhanced statistics using the same utility as merchant/brand pages
const couponStats = calculateCouponStats(allCategoryDeals || [], category?.name || '');

// Use centralized verification time generation for consistency
const recentVerifications = generateVerifications(category?.name || '', allCategoryDeals, 3);

// Generate enhanced SEO metadata and social proof
const seoMeta = category ? generateCategorySEOMeta(category.name, couponStats) : null;
const socialProof = category ? generateSocialProofText(category.name, 'category', couponStats) : null;

// Enhanced SEO metadata for category coupons
const title = seoMeta?.title || `${category.name} Coupon Codes & Discount Deals - Save Up to ${couponStats.bestDiscount} | VapeHybrid`;
const description = seoMeta?.description || `Get the latest ${category.name.toLowerCase()} coupon codes and discount deals. Save on premium ${category.name.toLowerCase()} with ${couponStats.activeCoupons} verified promo codes. Updated daily.`;

// Generate enhanced structured data for SEO
const structuredData = category ? generateCategoryPageSchema(
  category,
  deals || [],
  Astro.url.origin,
  couponStats
) : undefined;

// Fetch all merchants and brands for filter
const { data: allMerchants } = await supabase
  .from('merchants')
  .select('id, name')
  .eq('status', 'active')
  .order('name');

const { data: allBrands } = await supabase
  .from('brands')
  .select('id, name')
  .order('name');

// Calculate active filter count
const activeFilterCount = merchantIds.length + brandIds.length +
  (minPrice > 0 || maxPrice < 200 ? 1 : 0) +
  (minDiscount > 0 || maxDiscount < 100 ? 1 : 0) +
  (expiringSoon ? 1 : 0) +
  (validLongTerm ? 1 : 0);

// Generate meta tags object
const metaTags = seoMeta ? generateMetaTagsObject(seoMeta, Astro.url.href, category?.category_logo) : {};
---

<MainLayout
  title={title}
  description={description}
  structuredData={structuredData}
  openGraph={{
    basic: {
      title: metaTags['og:title'] || title,
      type: "website",
      image: metaTags['og:image'] || category?.category_logo,
      url: metaTags['og:url'] || Astro.url.href
    },
    optional: {
      description: metaTags['og:description'] || description,
      siteName: "VapeHybrid"
    },
    image: {
      alt: metaTags['og:image:alt'] || `${category?.name} Coupons`,
      width: 1200,
      height: 630
    }
  }}
  twitter={{
    card: "summary_large_image",
    site: "@vapehybrid",
    creator: "@vapehybrid",
    title: metaTags['twitter:title'] || title,
    description: metaTags['twitter:description'] || description,
    image: metaTags['twitter:image'] || category?.category_logo
  }}
>
  <!-- Seamless Category Section -->
  <div class="bg-[#fbfafc] dark:bg-gray-900 min-h-screen">
    <div class="max-w-[1280px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Breadcrumbs -->
      <nav class="mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
          <li><a href="/" class="hover:text-gray-900 dark:hover:text-white transition-colors">Home</a></li>
          <li><span class="mx-2 text-gray-400">/</span></li>
          <li><a href="/coupons" class="hover:text-gray-900 dark:hover:text-white transition-colors">Coupons</a></li>
          <li><span class="mx-2 text-gray-400">/</span></li>
          <li><a href="/coupons/categories" class="hover:text-gray-900 dark:hover:text-white transition-colors">Categories</a></li>
          <li><span class="mx-2 text-gray-400">/</span></li>
          <li class="text-gray-900 dark:text-white font-medium">{category.name}</li>
        </ol>
      </nav>

      <!-- Seamless Layout: 1/4 Left + 3/4 Right -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Left Sidebar (1/4 width) -->
        <div class="lg:col-span-1">
          <!-- Category Logo & Info Box -->
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6 mb-6">
            <!-- Category Logo -->
            <div class="flex justify-center mb-4">
              <div class="w-20 h-20 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 flex items-center justify-center overflow-hidden">
                {category.category_logo ? (
                  <WebPImage
                    src={category.category_logo}
                    alt={`${category.name} category icon`}
                    className="w-14 h-14 object-contain"
                    fallbackSrc="/icons/002-vape.png"
                  />
                ) : (
                  <div class="w-14 h-14 bg-blue-200 dark:bg-blue-700 rounded-lg flex items-center justify-center">
                    <span class="text-blue-600 dark:text-blue-300 font-bold text-xl">{category.name.charAt(0)}</span>
                  </div>
                )}
              </div>
            </div>

            <!-- Category Name & Title -->
            <h1 class="text-xl font-bold text-gray-900 dark:text-white text-center mb-2">
              {category.name}
            </h1>
            <p class="text-sm text-gray-600 dark:text-gray-300 text-center mb-4">
              Coupon Codes & Deals
            </p>

            <!-- Quick Stats -->
            <div class="space-y-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-300">Last ✓ Verified :</span>
                <span class="font-semibold text-gray-900 dark:text-white">8 hours ago</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-300">Last user 💰 Recent Saved:</span>
                <span class="font-semibold text-green-600">$23.50</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-300">Active Coupons:</span>
                <span class="font-semibold text-blue-600">{couponStats.activeCoupons}</span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600 dark:text-gray-300">Best Discount:</span>
                <span class="font-semibold text-green-600">{couponStats.bestDiscount}</span>
              </div>
            </div>
          </div>

          <!-- Sticky Email Capture -->
          <div class="sticky top-4">
            <UniversalEmailCaptureForm
              client:load
              targetName={category.name}
              subscriptionType="category"
              className="mb-4"
            />
          </div>
        </div>

        <!-- Right Content Area (3/4 width) -->
        <div class="lg:col-span-3">
          <!-- Main Title & Description -->
          <div class="mb-6">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3">
              {category.name} Coupon Codes & Discount Deals
            </h2>
            <p class="text-lg text-gray-600 dark:text-gray-300 mb-4">
              Get the latest {category.name.toLowerCase()} coupon codes and discount deals. Save on premium {category.name.toLowerCase()} products with verified promo codes updated daily.
            </p>
          </div>

          <!-- Enhanced Statistics Dashboard -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-blue-600 mb-1">{couponStats.activeCoupons}</div>
              <div class="text-xs text-gray-600 dark:text-gray-300">Active Coupons</div>
            </div>
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-green-600 mb-1">{couponStats.bestDiscount}</div>
              <div class="text-xs text-gray-600 dark:text-gray-300">Best Discount</div>
            </div>
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-purple-600 mb-1">{couponStats.avgSavings}</div>
              <div class="text-xs text-gray-600 dark:text-gray-300">Avg. Savings</div>
            </div>
            <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-orange-600 mb-1">{couponStats.successRate}</div>
              <div class="text-xs text-gray-600 dark:text-gray-300">Success Rate</div>
            </div>
          </div>

          <!-- Verification Info with Show More -->
          <div class="mb-6">
            <ExpandableCategoryInfo
              client:load
              categoryName={category.name}
              activeCoupons={couponStats.activeCoupons}
            />
          </div>

          <!-- Navigation Tabs -->
          <div class="flex gap-6 mb-6 border-b border-gray-200 dark:border-gray-600">
            <a href="#activity" class="pb-2 border-b-2 border-green-500 text-green-600 font-medium hover:text-green-700">Activity</a>
            <a href="#about" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">About</a>
            <a href="#faq" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">FAQ</a>
            <a href="#related" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">Related</a>
          </div>

          <!-- Controls Section -->
          <div class="mb-8">
            <!-- Desktop Controls -->
            <div class="hidden md:flex items-center justify-between gap-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div class="flex items-center gap-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                  {totalCount || 0} {category.name} Coupons Available
                </h2>
              </div>

              <div class="flex items-center gap-3">
                <FilterDrawer
                  categories={[]}
                  merchants={allMerchants || []}
                  brands={allBrands || []}
                  currentUrl={Astro.url.href}
                  activeFilterCount={activeFilterCount}
                  client:load
                />
                <SortSelect value={sort} client:load />
                <ViewToggle value={view} client:load />
              </div>
            </div>

            <!-- Mobile Controls -->
            <div class="md:hidden space-y-3">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white text-center">
                {totalCount || 0} {category.name} Coupons Available
              </h2>
              <div class="flex items-center justify-center gap-3">
                <FilterDrawer
                  categories={[]}
                  merchants={allMerchants || []}
                  brands={allBrands || []}
                  currentUrl={Astro.url.href}
                  activeFilterCount={activeFilterCount}
                  client:load
                />
                <SortSelect value={sort} client:load />
                <ViewToggle value={view} client:load />
              </div>
            </div>
          </div>

          <!-- Error Messages -->
          {categoryError && (
            <div class="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-md mb-6">
              <p>There was an error loading the category information. Please try again later.</p>
            </div>
          )}

          {dealsError && (
            <div class="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-md mb-6">
              <p>There was an error loading coupons. Please try again later.</p>
            </div>
          )}

          <!-- Coupons Grid/List -->
          {deals && deals.length > 0 ? (
            <div>
              <OptimizedDealsPageWrapper
                deals={deals}
                initialViewMode={view as 'grid' | 'list'}
                client:load
              />

              {/* Pagination */}
              <div class="mt-12 mb-8 flex flex-col md:flex-row justify-between items-center gap-6 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/10 dark:border-gray-600/10 rounded-xl p-4 shadow-sm">
                <PerPageSelect
                  value={limit}
                  options={[12, 24, 48, 96]}
                  url={url}
                />

                <Pagination
                  currentPage={page}
                  totalPages={totalPages}
                  baseUrl={`/coupons/categories/${slug}`}
                  searchParams={url.searchParams}
                />
              </div>
            </div>
          ) : (
            <div class="text-center py-12 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
              <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Coupons Found</h3>
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                We couldn't find any active coupons for {category.name.toLowerCase()} at the moment. Check back soon for new deals!
              </p>
              <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <GlowingButton client:load asLink={true} href="/coupons">
                  Browse All Coupons
                </GlowingButton>
                <a href="/coupons/categories" class="inline-flex items-center px-6 py-3 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                  Browse Other Categories
                </a>
              </div>
            </div>
          )}

          <!-- Enhanced Category Content Sections -->
          <div class="mt-8 space-y-8">
            <!-- Enhanced Activity Section -->
            <CategoryActivity
              client:load
              categoryName={category.name}
              activeCoupons={couponStats.activeCoupons}
              avgDiscount={couponStats.bestDiscount ? parseFloat(couponStats.bestDiscount.replace('%', '')) : 15}
              successRate={couponStats.successRate}
            />

            <!-- About Category Section -->
            <div class="mb-6">
              <CategoryAbout
                client:load
                categoryName={category.name}
              />
            </div>

            <!-- FAQ Section -->
            <div id="faq" class="mb-6">
              <CategoryFAQ
                client:load
                categoryName={category.name}
                activeCoupons={couponStats.activeCoupons}
                bestDiscount={couponStats.bestDiscount ? parseFloat(couponStats.bestDiscount.replace('%', '')) : 15}
              />
            </div>

            <!-- Enhanced Related Sections -->
            <div id="related">
              <CategoryRelatedSections
                client:load
                categoryName={category.name}
              />
            </div>

            <!-- Category Description (if available) -->
            {category.description && (
              <div class="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">About {category.name}</h3>
                <p class="text-gray-600 dark:text-gray-300">{category.description}</p>
              </div>
            )}

            <!-- Quick Navigation -->
            <div class="mt-8">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">More Category Coupons</h3>
              <div class="flex flex-wrap gap-3">
                <a href="/coupons/categories" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm">
                  View All Categories
                </a>
                <a href="/coupons" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
                  Browse All Coupons
                </a>
              </div>
            </div>
          </div>
        </div> <!-- Close right content area -->
      </div> <!-- Close seamless layout grid -->
    </div>
  </div>
</MainLayout>
