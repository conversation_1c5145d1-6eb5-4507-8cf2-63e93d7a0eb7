---
import MainLayout from '../../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../../utils/supabase/server';
import PageBreadcrumbs from '../../../components/PageBreadcrumbs.astro';
import { GlowingButton } from '../../../components/DesignSystem/Button/GlowingButton';
import WebPImage from '../../../components/DesignSystem/WebPImage.astro';

// Fetch all categories with deal counts
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

// Get all categories first
const { data, error } = await supabase
  .from('categories')
  .select('*')
  .order('name');

if (error) {
  console.error('Error fetching categories:', error);
}

// Define category type
interface Category {
  id: string;
  name: string;
  slug: string;
  category_logo?: string;
  description?: string;
}

const categories: Category[] = data || [];

// Enhanced SEO metadata targeting category-specific searches
const title = 'Vape Coupon Categories - E-Liquids, Disposables, Devices & More | VapeHybrid';
const description = 'Find vape coupons by category! Save on e-liquids, disposable vapes, pod systems, coils, and accessories. Hand-tested discount codes with real success rates from actual vapers. Updated daily with verified deals.';

// Enhanced structured data for category-specific searches
const structuredData = {
  '@context': 'https://schema.org',
  '@graph': [
    // Main CollectionPage
    {
      '@type': 'CollectionPage',
      '@id': Astro.url.href,
      name: 'Vape Coupon Categories',
      description: description,
      url: Astro.url.href,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: categories.length,
        itemListElement: categories.map((category, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'ProductCategory',
            '@id': `${Astro.url.origin}/coupons/categories/${category.slug}`,
            name: category.name,
            url: `${Astro.url.origin}/coupons/categories/${category.slug}`,
            description: category.description || `Save on ${category.name.toLowerCase()} with verified coupon codes`
          }
        }))
      }
    },
    // BreadcrumbList Schema
    {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: Astro.url.origin
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Coupons',
          item: `${Astro.url.origin}/coupons`
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: 'Category Coupons',
          item: Astro.url.href
        }
      ]
    },
    // FAQ Schema for category-related questions
    {
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: 'Which vape product categories have the best deals?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'E-liquids and disposable vapes typically offer the highest discount percentages, often 30-50% off. Device categories like pod systems and mods have frequent sales with substantial savings.'
          }
        },
        {
          '@type': 'Question',
          name: 'Do coupon codes work on all vape product categories?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Most coupon codes work across all categories, but some are category-specific. Premium e-liquid brands and new device releases may have restrictions. Check each coupon\'s terms for details.'
          }
        },
        {
          '@type': 'Question',
          name: 'How often do vape category deals change?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'We update category deals daily. Disposable vape deals change most frequently, while device and accessory deals typically run for longer periods. Subscribe for instant notifications of new category deals.'
          }
        }
      ]
    }
  ]
};

// Popular categories for featured section
const popularCategories = categories.slice(0, 6);
---

<MainLayout title={title} description={description} structuredData={structuredData}>
  <!-- Hero Section -->
  <div class="relative bg-gradient-to-br from-design-background via-design-background to-design-muted/20 py-16 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-30">
      <div class="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.15)_1px,_transparent_0)] bg-[length:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.05)_1px,_transparent_0)]"></div>
    </div>

    <!-- Content -->
    <div class="max-w-[1280px] mx-auto px-4 relative z-10">
      <div class="max-w-3xl mx-auto text-center">
        <PageBreadcrumbs 
          items={[
            { label: 'Home', href: '/' },
            { label: 'Coupons', href: '/coupons' },
            { label: 'Category Coupons', href: '/coupons/categories', current: true }
          ]}
          class="text-center" 
        />
        <h1 class="text-4xl md:text-5xl font-normal text-design-foreground mb-6 leading-tight">
          Vape Coupon Categories - Find Deals by What You Vape
        </h1>
        <p class="text-lg text-design-muted-foreground mb-8 max-w-2xl mx-auto">
          Shop smart by category! Whether you're stocking up on e-liquids, trying new disposables, upgrading your pod system, or building coils, we've organized the best deals by what you actually vape. Every code tested by real vapers.
        </p>
        
        <!-- Category Statistics -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-design-card/50 border border-design-border rounded-lg max-w-2xl mx-auto">
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">{categories.length}</div>
            <div class="text-sm text-design-muted-foreground">Categories</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">500+</div>
            <div class="text-sm text-design-muted-foreground">Active Coupons</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">50%</div>
            <div class="text-sm text-design-muted-foreground">Max Savings</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-primary">98%</div>
            <div class="text-sm text-design-muted-foreground">Success Rate</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-[960px] mx-auto px-4 pt-4 sm:pt-2 lg:pt-4 pb-12">
    {/* Error Message */}
    {error && (
      <div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-8">
        <p>There was an error loading category coupons. Please try again later.</p>
      </div>
    )}

    {/* Categories Grid - Enhanced for SEO and Responsive Design */}
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
      {categories.map((category: Category) => (
        <a
          href={`/coupons/categories/${category.slug}`}
          class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-primary rounded-xl p-6 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow h-full"
          aria-label={`Browse ${category.name} vape coupons`}
        >
          {/* Category image with proper alignment */}
          <div class="w-20 h-20 mb-4 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative">
            {category.category_logo ? (
              <WebPImage
                src={category.category_logo}
                alt={`${category.name} category icon`}
                class="w-12 h-12 object-contain group-hover:scale-110 transition-transform duration-300"
                fallbackSrc="/icons/002-vape.png"
              />
            ) : (
              <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span class="text-primary font-bold text-lg">{category.name.charAt(0)}</span>
              </div>
            )}

            {/* Hover overlay */}
            <div class="absolute inset-0 bg-primary/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          {/* Category info */}
          <h3 class="text-lg font-semibold text-design-foreground group-hover:text-primary transition-colors mb-2">
            {category.name}
          </h3>

          {/* Coupon info */}
          <div class="text-sm text-design-muted-foreground mb-4">
            <span class="inline-flex items-center px-2 py-1 rounded-full bg-primary/10 text-primary">
              View Coupon Codes
            </span>
          </div>

          <p class="text-sm text-design-muted-foreground text-center line-clamp-2">
            {category.description || `Browse all ${category.name.toLowerCase()} coupons and discount codes`}
          </p>

          {/* Hover arrow */}
          <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </a>
      ))}
    </div>

    {/* Call to Action */}
    <div class="mt-16 text-center p-8 bg-gradient-to-r from-primary/5 to-design-secondary/5 rounded-xl border border-design-border mx-auto">
      <h3 class="text-2xl font-bold text-design-foreground mb-4">Looking for Something Specific?</h3>
      <p class="text-design-muted-foreground mb-6 max-w-2xl mx-auto">
        Can't find the category you're looking for? Browse all available coupons or use our search to find specific products and brands.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <GlowingButton client:load asLink={true} href="/coupons" className="text-sm px-6 py-2.5">
          Browse All Coupons
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
        </GlowingButton>
        <a href="/coupons?search=" class="inline-flex items-center px-6 py-2.5 border border-design-border rounded-lg text-design-foreground hover:bg-design-muted/10 transition-colors">
          Search Coupons
        </a>
      </div>
    </div>
  </div>
</MainLayout>
