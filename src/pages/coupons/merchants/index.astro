---
import MainLayout from '../../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../../utils/supabase/server';
import PageBreadcrumbs from '../../../components/PageBreadcrumbs.astro';
import { GlowingButton } from '../../../components/DesignSystem/Button/GlowingButton';

// Define merchant type
interface Merchant {
  id: string;
  name: string;
  slug?: string;
  logo_url?: string;
  website_url?: string;
  status: string;
}

// Fetch all merchants
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

const { data, error } = await supabase
  .from('merchants')
  .select('*')
  .eq('status', 'active')
  .order('name');

if (error) {
  console.error('Error fetching merchants:', error);
}

const merchants = data || [];

// Enhanced SEO metadata targeting GSC keywords
const title = 'Vape Store Coupons & Promo Codes - EJuice Connect, VaporDNA & More | VapeHybrid';
const description = 'Get verified coupon codes for top vape stores like EJuice Connect, VaporDNA, EightVape, and Element Vape. Hand-tested promo codes with real success rates from actual vapers. Save up to 50% on e-liquids, devices, and accessories.';

// Enhanced structured data targeting merchant-specific searches
const structuredData = {
  '@context': 'https://schema.org',
  '@graph': [
    // Main CollectionPage
    {
      '@type': 'CollectionPage',
      '@id': Astro.url.href,
      name: 'Vape Store Coupons Directory',
      description: description,
      url: Astro.url.href,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: merchants.length,
        itemListElement: data?.map((merchant, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'LocalBusiness',
            '@id': `${Astro.url.origin}/coupons/merchants/${merchant.slug || merchant.id}`,
            name: merchant.name,
            url: `${Astro.url.origin}/coupons/merchants/${merchant.slug || merchant.id}`,
            logo: merchant.logo_url || undefined,
            description: `Save with verified ${merchant.name} coupon codes and promo codes`
          }
        }))
      }
    },
    // BreadcrumbList Schema
    {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: Astro.url.origin
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Coupons',
          item: `${Astro.url.origin}/coupons`
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: 'Store Coupons',
          item: Astro.url.href
        }
      ]
    },
    // FAQ Schema targeting merchant questions
    {
      '@type': 'FAQPage',
      mainEntity: [
        {
          '@type': 'Question',
          name: 'Which vape stores have the best coupon codes?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Top vape stores like EJuice Connect, VaporDNA, EightVape, and Element Vape regularly offer exclusive coupon codes with savings up to 50%. All codes are verified daily by our community.'
          }
        },
        {
          '@type': 'Question',
          name: 'How do I use vape store coupon codes?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'Copy the coupon code, add items to your cart on the vape store website, and paste the code at checkout. Most stores have a "promo code" or "coupon code" field during payment.'
          }
        },
        {
          '@type': 'Question',
          name: 'Do vape store coupons work on sale items?',
          acceptedAnswer: {
            '@type': 'Answer',
            text: 'This varies by store. Some vape merchants allow coupon codes on sale items, while others exclude clearance products. Check the coupon terms or contact the store directly.'
          }
        }
      ]
    }
  ]
};
---

<MainLayout title={title} description={description} structuredData={structuredData}>
  <!-- Consistent Hero Section matching brands/categories pages -->
  <div class="bg-design-background py-16">
    <div class="max-w-[960px] mx-auto px-4 text-center">
      <!-- Breadcrumbs -->
      <PageBreadcrumbs
        items={[
          { label: 'Home', href: '/' },
          { label: 'Coupons', href: '/coupons' },
          { label: 'Merchants', href: '/coupons/merchants', current: true }
        ]}
        class="text-center mb-8"
      />

      <!-- Header Section -->
      <div class="mb-8">
        <h1 class="text-4xl md:text-5xl font-normal text-design-foreground mb-6 leading-tight">
          Vape Store Coupons & Promo Codes - Save at Your Favorite Shops
        </h1>
        <p class="text-lg text-design-muted-foreground mb-8 max-w-2xl mx-auto">
          Score deals at top vape shops like EJuice Connect, VaporDNA, EightVape, and Element Vape! We test every coupon code with real purchases so you know they actually work. Save up to 50% on everything from premium e-liquids to the latest devices.
        </p>
      </div>

      <!-- Statistics Dashboard matching other pages -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-design-card/50 border border-design-border rounded-lg max-w-2xl mx-auto">
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">{merchants.length}</div>
          <div class="text-sm text-design-muted-foreground">Stores</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">400+</div>
          <div class="text-sm text-design-muted-foreground">Active Coupons</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">50%</div>
          <div class="text-sm text-design-muted-foreground">Max Savings</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">98%</div>
          <div class="text-sm text-design-muted-foreground">Success Rate</div>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-[960px] mx-auto px-4 pt-4 sm:pt-8 lg:pt-12 pb-12">
    {/* Error Message */}
    {error && (
      <div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-8">
        <p>There was an error loading merchant coupons. Please try again later.</p>
      </div>
    )}

    {/* Featured Merchants Section - Consistent with brands page */}
    <div class="mb-12">
      <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">Top Vape Shops with Verified Coupon Codes</h2>
      <p class="text-design-muted-foreground mb-8 text-center">These vape retailers consistently offer the best discounts and highest coupon success rates among our community of vapers. All codes hand-verified daily.</p>

      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {merchants.slice(0, 8).map((merchant) => (
          <a
            href={`/coupons/merchants/${merchant.slug || merchant.id}`}
            class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-4 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow"
          >
            {/* Merchant logo - consistent with brands design */}
            <div class="w-16 h-16 mb-3 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative">
              {merchant.logo_url ? (
                <img
                  src={merchant.logo_url}
                  alt={`${merchant.name} logo`}
                  class="w-10 h-10 object-contain"
                  loading="lazy"
                />
              ) : (
                <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center">
                  <span class="text-primary font-bold text-sm">{merchant.name.charAt(0)}</span>
                </div>
              )}
            </div>

            {/* Merchant name - consistent sizing */}
            <h3 class="font-semibold text-design-foreground group-hover:text-primary transition-colors text-sm line-clamp-2">
              {merchant.name}
            </h3>
            <p class="text-xs text-design-muted-foreground mt-1">View Coupons</p>
          </a>
        ))}
      </div>
    </div>

    {/* All Merchants Grid - Consistent with brands page */}
    <div class="space-y-16">
      <div>
        <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">All Vape Store Coupons</h2>
        <p class="text-design-muted-foreground mb-8 text-center">Browse all vape merchants and find verified coupon codes from trusted online stores</p>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          {data.map((merchant) => (
            <a
              href={`/coupons/merchants/${merchant.slug || merchant.id}`}
              class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-6 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow"
            >
              {/* Merchant image with proper sizing */}
              <div class="w-20 h-20 mb-4 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative">
                {merchant.logo_url ? (
                  <img
                    src={merchant.logo_url}
                    alt={`${merchant.name} logo`}
                    class="w-12 h-12 object-contain group-hover:scale-110 transition-transform duration-300"
                    loading="lazy"
                  />
                ) : (
                  <div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <span class="text-primary font-bold text-lg">{merchant.name.charAt(0)}</span>
                  </div>
                )}

                {/* Hover overlay */}
                <div class="absolute inset-0 bg-primary/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* Merchant info */}
              <h3 class="text-lg font-semibold text-design-foreground group-hover:text-primary transition-colors mb-2 line-clamp-2">
                {merchant.name}
              </h3>

              {/* Coupon info */}
              <div class="text-sm text-design-muted-foreground mb-4">
                <span class="inline-flex items-center px-2 py-1 rounded-full bg-primary/10 text-primary">
                  View Coupon Codes
                </span>
              </div>

              {merchant.short_description && (
                <p class="text-sm text-design-muted-foreground text-center line-clamp-2">
                  {merchant.short_description}
                </p>
              )}

              {/* Hover arrow */}
              <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </div>
            </a>
          ))}
        </div>
      </div>
    </div>

    {/* Call to Action - Consistent with other pages */}
    <div class="mt-16 text-center p-8 bg-gradient-to-r from-primary/5 to-design-secondary/5 rounded-xl border border-design-border mx-auto">
      <h3 class="text-2xl font-bold text-design-foreground mb-4">Looking for Specific Categories or Brands?</h3>
      <p class="text-design-muted-foreground mb-6 max-w-2xl mx-auto">
        Browse our complete collection of vape coupons organized by product categories and brands for more targeted savings.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <GlowingButton client:load asLink={true} href="/coupons/categories" className="text-sm px-6 py-2.5">
          Browse Categories
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg>
        </GlowingButton>
        <a href="/coupons/brands" class="inline-flex items-center px-6 py-2.5 border border-design-border rounded-lg text-design-foreground hover:bg-design-muted/10 transition-colors">
          Browse Brands
        </a>
      </div>
    </div>
  </div>
</MainLayout>
