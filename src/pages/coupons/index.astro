---
import MainLayout from '../../layouts/MainLayout.astro';
import OptimizedDealsPageWrapper from '../../components/OptimizedDealsPageWrapper.tsx';
import FilterDrawer from '../../components/DesignSystem/FilterDrawer.tsx';
import SortSelect from '../../components/DesignSystem/SortSelect.tsx';
import ViewToggle from '../../components/DesignSystem/ViewToggle.tsx';
import SearchBox from '../../components/DesignSystem/SearchBox.tsx';
import Pagination from '../../components/DesignSystem/Pagination.astro';
import PerPageSelect from '../../components/DesignSystem/PerPageSelect.astro';
import DealsFAQ from '../../components/DealsFAQ.tsx';
import NoticeBar from '../../components/DesignSystem/NoticeBar.tsx';
import { createServerSupabaseClient } from '../../utils/supabase/server.js';

// Create server-side Supabase client
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

// Get query parameters
const url = new URL(Astro.request.url);
const viewMode = (url.searchParams.get('view') || 'grid') as 'grid' | 'list';
const sortBy = (url.searchParams.get('sort') || 'newest') as 'newest' | 'price_asc' | 'price_desc' | 'expiring_soon' | 'discount_desc' | 'most_popular';
const page = parseInt(url.searchParams.get('page') || '1');
const limit = parseInt(url.searchParams.get('limit') || '24');

// Filter parameters
const searchQuery = url.searchParams.get('search') || '';
const categoryIds = url.searchParams.getAll('category').map(id => parseInt(id)).filter(id => !isNaN(id));
const merchantIds = url.searchParams.getAll('merchant').map(id => parseInt(id)).filter(id => !isNaN(id));
const brandIds = url.searchParams.getAll('brand').map(id => parseInt(id)).filter(id => !isNaN(id));
const featured = url.searchParams.get('featured') === 'true';
const validLongTerm = url.searchParams.get('validLongTerm') === 'true';

// Build the query
let query = supabase
  .from('deals')
  .select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `);

// Apply search filter if provided
if (searchQuery && searchQuery.length >= 2) {
  query = query.or(`title.ilike.%${searchQuery}%, description.ilike.%${searchQuery}%, coupon_code.ilike.%${searchQuery}%`);
}

// Apply filters
if (categoryIds.length > 0) {
  query = query.in('category_id', categoryIds);
}

if (merchantIds.length > 0) {
  query = query.in('merchant_id', merchantIds);
}

if (brandIds.length > 0) {
  query = query.in('brand_id', brandIds);
}

if (featured) {
  query = query.eq('is_featured', true);
}

// Filter for active deals only
const now = new Date();
query = query.gt('deal_end_date', now.toISOString());

// Apply sorting
switch (sortBy) {
  case 'newest':
    query = query.order('created_at', { ascending: false });
    break;
  case 'price_asc':
    query = query.order('price', { ascending: true, nullsLast: true });
    break;
  case 'price_desc':
    query = query.order('price', { ascending: false, nullsLast: true });
    break;
  case 'expiring_soon':
    query = query.order('deal_end_date', { ascending: true });
    break;
  case 'discount_desc':
    // Try to order by discount field first, then by discount_percentage
    query = query.order('discount', { ascending: false, nullsLast: true });
    break;
  case 'most_popular':
    query = query.order('click_count', { ascending: false, nullsLast: true });
    break;
  default:
    query = query.order('created_at', { ascending: false });
}

// Apply pagination
const offset = (page - 1) * limit;
query = query.range(offset, offset + limit - 1);

// Execute the query
const { data: deals, error: dealsError } = await query;

// Get total count for pagination
let countQuery = supabase
  .from('deals')
  .select('id', { count: 'exact', head: true });

// Apply same filters for count
if (searchQuery && searchQuery.length >= 2) {
  countQuery = countQuery.or(`title.ilike.%${searchQuery}%, description.ilike.%${searchQuery}%, coupon_code.ilike.%${searchQuery}%`);
}

if (categoryIds.length > 0) {
  countQuery = countQuery.in('category_id', categoryIds);
}

if (merchantIds.length > 0) {
  countQuery = countQuery.in('merchant_id', merchantIds);
}

if (brandIds.length > 0) {
  countQuery = countQuery.in('brand_id', brandIds);
}

if (featured) {
  countQuery = countQuery.eq('is_featured', true);
}

countQuery = countQuery.gt('deal_end_date', now.toISOString());

const { count: totalCount, error: countError } = await countQuery;

// Fetch filter options
const { data: categories } = await supabase
  .from('categories')
  .select('id, name, slug')
  .order('name');

const { data: merchants } = await supabase
  .from('merchants')
  .select('id, name, slug')
  .order('name');

const { data: brands } = await supabase
  .from('brands')
  .select('id, name, slug')
  .order('name');

// Calculate pagination
const totalPages = Math.ceil((totalCount || 0) / limit);

// Enhanced SEO metadata optimized for GSC issues
const generateDynamicTitle = () => {
  let title = 'Best Vape Coupons & Discount Codes - Save Up to 50%';

  if (searchQuery) {
    title = `${searchQuery} Coupons & Promo Codes - Verified Daily`;
  } else if (categoryIds.length === 1 && categories) {
    const category = categories.find(c => c.id === categoryIds[0]);
    if (category) {
      title = `${category.name} Coupons & Discount Codes - Save Big`;
    }
  } else if (brandIds.length === 1 && brands) {
    const brand = brands.find(b => b.id === brandIds[0]);
    if (brand) {
      title = `${brand.name} Coupon Codes & Promo Codes - Exclusive Deals`;
    }
  } else if (merchantIds.length === 1 && merchants) {
    const merchant = merchants.find(m => m.id === merchantIds[0]);
    if (merchant) {
      title = `${merchant.name} Coupon Codes & Promo Codes - Verified`;
    }
  } else if (featured) {
    title = 'Featured Vape Coupons & Best Deals - Hand-Picked';
  }

  return `${title} | VapeHybrid`;
};

const generateDynamicDescription = () => {
  let description = 'Find verified vape coupons and discount codes from top brands like Geek Bar, Lost Mary, and Voopoo. Save up to 50% on e-liquids, disposables, and devices with hand-tested promo codes updated daily by real vapers';

  if (searchQuery) {
    description = `Find verified ${searchQuery} coupons and promo codes. Save with exclusive discount codes tested by actual vapers`;
  } else if (categoryIds.length === 1 && categories) {
    const category = categories.find(c => c.id === categoryIds[0]);
    if (category) {
      description = `Get the latest ${category.name.toLowerCase()} coupons and discount codes. Save on premium ${category.name.toLowerCase()} with verified promo codes tested by vapers`;
    }
  } else if (brandIds.length === 1 && brands) {
    const brand = brands.find(b => b.id === brandIds[0]);
    if (brand) {
      description = `Save with verified ${brand.name} coupon codes and promo codes. Get exclusive discounts on ${brand.name} products with real success rates`;
    }
  } else if (merchantIds.length === 1 && merchants) {
    const merchant = merchants.find(m => m.id === merchantIds[0]);
    if (merchant) {
      description = `Get the best ${merchant.name} coupon codes and promo codes. Save with verified discount codes tested by our vaper community`;
    }
  }

  return `${description}. 98% success rate with real-time verification.`;
};

const dynamicTitle = generateDynamicTitle();
const dynamicDescription = generateDynamicDescription();

// Enhanced structured data optimized for search visibility
const couponsPageStructuredData = {
  '@context': 'https://schema.org',
  '@graph': [
    // Main CollectionPage
    {
      '@type': 'CollectionPage',
      '@id': Astro.url.href,
      name: 'Best Vape Coupons & Discount Codes',
      description: dynamicDescription,
      url: Astro.url.href,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: totalCount || 0,
        itemListElement: deals?.slice(0, 10).map((deal, index) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Offer',
            name: deal.title,
            description: deal.description || `Save with verified ${deal.title} coupon code`,
            url: `${Astro.url.origin}/coupon/${deal.normalized_title || deal.cleaned_title || deal.id}`,
            price: deal.price || undefined,
            priceCurrency: 'USD',
            availability: 'https://schema.org/InStock',
            validThrough: deal.deal_end_date,
            seller: {
              '@type': 'Organization',
              name: deal.merchants?.name || 'VapeHybrid Partner'
            }
          }
        })) || []
      }
    },
    // Website Schema with Search Action
    {
      '@type': 'WebSite',
      '@id': `${Astro.url.origin}/#website`,
      name: 'VapeHybrid',
      url: Astro.url.origin,
      potentialAction: {
        '@type': 'SearchAction',
        target: {
          '@type': 'EntryPoint',
          urlTemplate: `${Astro.url.origin}/coupons?search={search_term_string}`
        },
        'query-input': 'required name=search_term_string'
      }
    },
    // Organization Schema
    {
      '@type': 'Organization',
      '@id': `${Astro.url.origin}/#organization`,
      name: 'VapeHybrid',
      url: Astro.url.origin,
      logo: `${Astro.url.origin}/logo.png`,
      description: 'The largest collection of verified vape coupons and discount codes, tested daily by real vapers.',
      sameAs: [
        'https://twitter.com/vapehybrid',
        'https://facebook.com/vapehybrid'
      ]
    },
    // BreadcrumbList Schema
    {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: Astro.url.origin
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Vape Coupons',
          item: Astro.url.href
        }
      ]
    }
  ]
};
---

<MainLayout
  title={dynamicTitle}
  description={dynamicDescription}
  structuredData={couponsPageStructuredData}>

  <NoticeBar
    message="We may earn commission when you use these coupons."
    linkText="Learn more"
    linkUrl="/terms"
    variant="default"
    client:load
  />

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-3 pb-8">
    <!-- Enhanced Header Section with Clear Value Proposition -->
    <div class="mb-8 text-center py-8">
      <h1 class="text-4xl md:text-5xl font-normal text-design-foreground mb-6 leading-tight max-w-4xl mx-auto">
        {searchQuery ? `${searchQuery} Coupons & Promo Codes` : 'Best Vape Coupons & Discount Codes for Vapers'}
      </h1>
      <p class="text-lg text-design-muted-foreground max-w-3xl mx-auto mb-8">
        {searchQuery
          ? `Find verified ${searchQuery} coupon codes and promo codes tested by real vapers. Save with exclusive discount deals updated daily with 98% success rate.`
          : 'Get verified vape coupon codes from top brands like Geek Bar, Lost Mary, and Voopoo. All codes hand-tested by actual vapers with real success rates. Save up to 50% on e-liquids, disposables, and devices.'
        }
      </p>

      <!-- Quick Navigation to Archive Pages -->
      <div class="flex flex-wrap justify-center gap-4 mb-8">
        <a href="/coupons/brands" class="inline-flex items-center px-6 py-3 bg-primary/10 hover:bg-primary text-primary hover:text-white rounded-lg transition-all font-medium">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
          Shop by Brand
        </a>
        <a href="/coupons/merchants" class="inline-flex items-center px-6 py-3 bg-primary/10 hover:bg-primary text-primary hover:text-white rounded-lg transition-all font-medium">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
          </svg>
          Shop by Store
        </a>
        <a href="/coupons/categories" class="inline-flex items-center px-6 py-3 bg-primary/10 hover:bg-primary text-primary hover:text-white rounded-lg transition-all font-medium">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
          </svg>
          Shop by Category
        </a>
      </div>

      <!-- Enhanced Coupon Statistics -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-design-card/50 border border-design-border rounded-lg max-w-2xl mx-auto">
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">{totalCount || 0}</div>
          <div class="text-sm text-design-muted-foreground">Active Coupons</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">50%</div>
          <div class="text-sm text-design-muted-foreground">Max Savings</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">$12.50</div>
          <div class="text-sm text-design-muted-foreground">Avg. Savings</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-primary">98%</div>
          <div class="text-sm text-design-muted-foreground">Success Rate</div>
        </div>
      </div>
    </div>

    <!-- Controls Section -->
    <div class="mb-8">
      <!-- Desktop Controls -->
      <div class="hidden md:flex items-center justify-between gap-4 p-4 bg-design-card/30 border border-design-border rounded-lg">
        <div class="flex items-center gap-4">
          <div class="max-w-md">
            <SearchBox
              placeholder="Search coupons..."
              value={searchQuery}
              client:load
            />
          </div>
          <FilterDrawer
            categories={categories || []}
            merchants={merchants || []}
            brands={brands || []}
            currentUrl={Astro.url.href}
            activeFilterCount={categoryIds.length + merchantIds.length + brandIds.length + (featured ? 1 : 0) + (validLongTerm ? 1 : 0)}
            client:load
          />
        </div>

        <div class="flex items-center gap-3">
          <SortSelect value={sortBy} client:load />
          <ViewToggle value={viewMode} client:load />
        </div>
      </div>

      <!-- Mobile Controls -->
      <div class="md:hidden space-y-3">
        <SearchBox
          placeholder="Search coupons..."
          value={searchQuery}
          client:load
        />
        <div class="flex items-center justify-between gap-3">
          <FilterDrawer
            categories={categories || []}
            merchants={merchants || []}
            brands={brands || []}
            currentUrl={Astro.url.href}
            activeFilterCount={categoryIds.length + merchantIds.length + brandIds.length + (featured ? 1 : 0) + (validLongTerm ? 1 : 0)}
            client:load
          />
          <div class="flex items-center gap-3">
            <SortSelect value={sortBy} client:load />
            <ViewToggle value={viewMode} client:load />
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-8">
      <!-- Coupons Grid/List -->
      <div class="w-full">
        {deals && deals.length > 0 ? (
          <div>
            <OptimizedDealsPageWrapper
              deals={deals}
              initialViewMode={viewMode}
              client:load
            />

            {/* Pagination controls */}
            <div class="mt-12 mb-8 flex flex-col md:flex-row justify-between items-center gap-6 bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 rounded-xl p-4 shadow-sm">
              <PerPageSelect
                value={limit}
                options={[12, 24, 48, 96]}
                url={url}
              />
              
              <Pagination
                currentPage={page}
                totalPages={totalPages}
                baseUrl="/coupons"
                searchParams={url.searchParams}
              />
            </div>
          </div>
        ) : (
          <div class="text-center py-16 bg-design-card border border-design-border rounded-lg">
            <h3 class="text-xl font-semibold text-design-foreground mb-2">No Coupons Found</h3>
            <p class="text-design-muted-foreground mb-6">
              {searchQuery 
                ? `We couldn't find any coupons matching "${searchQuery}". Try adjusting your search or browse all coupons.`
                : 'We couldn\'t find any coupons matching your filters. Try adjusting your filters or browse all coupons.'
              }
            </p>
            <a href="/coupons" class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
              Browse All Coupons
            </a>
          </div>
        )}

        {dealsError && (
          <div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-6">
            <p>There was an error loading coupons. Please try again later.</p>
          </div>
        )}
      </div>
    </div>

    <!-- Enhanced SEO Content Section with 960px width -->
    <div class="mt-16">
      <div class="max-w-[960px] mx-auto px-4 space-y-12">
        <!-- Value Proposition Section -->
        <div class="bg-design-card border border-design-border rounded-xl p-8">
          <h2 class="text-3xl font-bold text-design-foreground mb-6 text-center">
            Why VapeHybrid Has the Best Vape Coupons
          </h2>
          <div class="grid md:grid-cols-3 gap-8">
            <div class="text-center">
              <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-design-foreground mb-3">
                Hand-Tested by Real Vapers
              </h3>
              <p class="text-design-muted-foreground">
                Every coupon code is tested by actual vapers before we post it. No fake codes, no expired deals - just working discounts that save you money on your favorite vape gear.
              </p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-design-foreground mb-3">
                Updated Daily for Maximum Savings
              </h3>
              <p class="text-design-muted-foreground">
                Our team tracks new deals and removes expired codes every single day. You'll always find the freshest discounts from top vape brands and merchants.
              </p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-design-foreground mb-3">
                Largest Vape Coupon Community
              </h3>
              <p class="text-design-muted-foreground">
                Join thousands of vapers who save money with our exclusive deals. We partner directly with brands like Geek Bar, Lost Mary, and Voopoo for member-only discounts.
              </p>
            </div>
          </div>
        </div>

        <!-- How We Serve Section -->
        <div class="bg-gradient-to-r from-primary/5 to-design-secondary/5 rounded-xl p-8">
          <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">
            How VapeHybrid Saves You Money on Vaping
          </h2>
          <div class="grid md:grid-cols-2 gap-8">
            <div>
              <h3 class="text-lg font-semibold text-design-foreground mb-3">
                What We Serve: Verified Vape Deals
              </h3>
              <ul class="space-y-2 text-design-muted-foreground">
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Coupon codes for e-liquids, disposables, and devices
                </li>
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Exclusive promo codes from top vape brands
                </li>
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Store-wide discounts from trusted vape merchants
                </li>
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Limited-time flash sales and clearance deals
                </li>
              </ul>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-design-foreground mb-3">
                How We Serve It: Community-Verified
              </h3>
              <ul class="space-y-2 text-design-muted-foreground">
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Real vapers test every code before posting
                </li>
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Daily verification ensures codes actually work
                </li>
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Success rates tracked by actual user feedback
                </li>
                <li class="flex items-start">
                  <span class="text-primary mr-2">•</span>
                  Organized by brand, store, and product category
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- FAQ Section -->
        <div>
          <DealsFAQ client:load />
        </div>
      </div>
    </div>
  </div>


</MainLayout>
