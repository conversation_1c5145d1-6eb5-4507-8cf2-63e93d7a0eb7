---
import '../styles/main.css';
import HomeLayout from '../layouts/HomeLayout.astro';
import { EnhancedHero } from '../components/ui/homepage/enhanced-hero';
import { CategoryNav } from '../components/ui/homepage/category-nav';
import { FeaturedDeals } from '../components/ui/homepage/featured-deals';
import { WhyTrustUs } from '../components/ui/homepage/why-trust-us';
import { ComparisonTable } from '../components/ui/homepage/comparison-table';
import { NewsletterSection } from '../components/ui/homepage/newsletter-section';
import { TestimonialsSection } from '../components/ui/homepage/testimonials-section';
import { FAQSection } from '../components/ui/homepage/faq-section';
import { FlashDeals } from '../components/ui/homepage/flash-deals';
import { EnhancedBrandsSection } from '../components/ui/homepage/enhanced-brands-section';
import { HowItWorks } from '../components/ui/homepage/how-it-works';
import { RealTimeSavings } from '../components/ui/homepage/real-time-savings';
import { SimplifiedBrandGrid } from '../components/ui/homepage/SimplifiedBrandGrid';
import Section from '../components/ui/Section.astro';
// import { HomeButton } from '../components/ui/home-button';
import { createServerSupabaseClient } from '../utils/supabase/server';

// Enhanced SEO metadata targeting GSC keywords with vape community language
const title = "Best Vape Coupons & Discount Codes - Tested by Real Vapers | VapeHybrid";
const description = "Score verified vape coupon codes from Geek Bar, Lost Mary, Voopoo & SMOK! Fellow vapers test every code daily. Save up to 50% on your all-day vapes, sub-ohm setups, and nic salts. No expired codes, just working discounts for the vape community.";

// Enhanced structured data targeting vape coupon searches
const websiteStructuredData = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "VapeHybrid - Vape Coupons & Discount Codes",
  "alternateName": "VapeHybrid Coupon Site",
  "url": Astro.url.origin,
  "potentialAction": [
    {
      "@type": "SearchAction",
      "target": `${Astro.url.origin}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string"
    },
    {
      "@type": "SearchAction",
      "target": `${Astro.url.origin}/coupons?search={search_term_string}`,
      "query-input": "required name=search_term_string"
    }
  ],
  "description": description,
  "keywords": "vape coupons, vape discount codes, vape deals, ejuice connect coupon, geek bar coupons, lost mary discount, voopoo promo codes, vape community savings",
  "audience": {
    "@type": "Audience",
    "audienceType": "Vaping Community"
  },
  "publisher": {
    "@type": "Organization",
    "name": "VapeHybrid",
    "logo": {
      "@type": "ImageObject",
      "url": `${Astro.url.origin}/Vapehybrid light icon.svg`
    }
  }
};

// Enhanced organization structured data with LocalBusiness
const organizationStructuredData = {
  "@context": "https://schema.org",
  "@type": ["Organization", "LocalBusiness"],
  "name": "VapeHybrid",
  "alternateName": "VapeHybrid Coupon Platform",
  "url": Astro.url.origin,
  "logo": `${Astro.url.origin}/Vapehybrid light icon.svg`,
  "description": "The vaping community's trusted source for verified coupon codes and discount deals from top vape brands",
  "foundingDate": "2018",
  "slogan": "Verified Vape Coupons That Actually Work",
  "knowsAbout": [
    "Vape Coupons",
    "E-cigarette Discounts",
    "Vaping Community",
    "Coupon Verification",
    "Vape Brand Partnerships"
  ],
  "serviceArea": {
    "@type": "Country",
    "name": "United States"
  },
  "sameAs": [
    "https://twitter.com/vapehybrid",
    "https://facebook.com/vapehybrid",
    "https://instagram.com/vapehybrid"
  ],
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": "",
    "contactType": "customer service",
    "email": "<EMAIL>",
    "availableLanguage": "English",
    "areaServed": "US"
  },
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "4.8",
    "reviewCount": "1200",
    "bestRating": "5",
    "worstRating": "1"
  }
};

// FAQ Schema for home page targeting GSC keywords
const faqStructuredData = {
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.slice(0, 8).map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
};

// Combine basic structured data
// const baseStructuredData = [websiteStructuredData, organizationStructuredData];
// Not used directly, kept for reference only

// Create server-side Supabase client
const supabase = createServerSupabaseClient(Astro);

// Fetch featured deals
const { data: featuredDeals } = await supabase
  .from('deals')
  .select(`
    id,
    title,
    cleaned_title,
    discount,
    image_url,
    imagebig_url,
    tracking_url,
    merchants:merchant_id (name),
    brand_id,
    brands:brand_id (name, logo_url),
    is_featured,
    deal_end_date
  `)
  .order('created_at', { ascending: false })
  .limit(6);

// Fetch expiring deals (within 24 hours)
const now = new Date();
const in24Hours = new Date(now);
in24Hours.setHours(now.getHours() + 24);

const { data: expiringDeals24h } = await supabase
  .from('deals')
  .select(`
    id,
    title,
    cleaned_title,
    discount,
    image_url,
    imagebig_url,
    tracking_url,
    merchants:merchant_id (name),
    brand_id,
    brands:brand_id (name, logo_url),
    deal_end_date
  `)
  .not('deal_end_date', 'is', null)
  .gte('deal_end_date', now.toISOString())
  .lte('deal_end_date', in24Hours.toISOString())
  .order('deal_end_date', { ascending: true })
  .limit(6);

// If no deals expiring in 24 hours, try 72 hours
let flashDeals = expiringDeals24h;
if (!flashDeals || flashDeals.length === 0) {
  const in72Hours = new Date(now);
  in72Hours.setHours(now.getHours() + 72);

  const { data: expiringDeals72h } = await supabase
    .from('deals')
    .select(`
      id,
      title,
      cleaned_title,
      discount,
      image_url,
      imagebig_url,
      tracking_url,
      merchants:merchant_id (name),
      brand_id,
      brands:brand_id (name, logo_url),
      deal_end_date
    `)
    .not('deal_end_date', 'is', null)
    .gte('deal_end_date', now.toISOString())
    .lte('deal_end_date', in72Hours.toISOString())
    .order('deal_end_date', { ascending: true })
    .limit(6);

  flashDeals = expiringDeals72h;

  // If still no deals, try 7 days
  if (!flashDeals || flashDeals.length === 0) {
    const in7Days = new Date(now);
    in7Days.setDate(now.getDate() + 7);

    const { data: expiringDeals7d } = await supabase
      .from('deals')
      .select(`
        id,
        title,
        cleaned_title,
        discount,
        image_url,
        imagebig_url,
        tracking_url,
        merchants:merchant_id (name),
        brand_id,
        brands:brand_id (name, logo_url),
        deal_end_date
      `)
      .not('deal_end_date', 'is', null)
      .gte('deal_end_date', now.toISOString())
      .lte('deal_end_date', in7Days.toISOString())
      .order('deal_end_date', { ascending: true })
      .limit(6);

    flashDeals = expiringDeals7d;
  }
}

// Define the Deal type to match the FeaturedDeals component
type Deal = {
  id: number | string;
  title: string;
  cleaned_title?: string;
  discount?: string | number;
  imagebig_url?: string;
  image_url?: string;
  tracking_url?: string;
  merchant_name?: string;
  brand_name?: string;
  brand_id?: number;
  brand_logo_url?: string;
  is_featured?: boolean;
  expires_at?: string;
  deal_end_date?: string;
};

// Process deals data
const processedDeals: Deal[] = featuredDeals?.map((deal: any) => {
  // Safe access to nested properties
  const getBrandName = () => {
    if (!deal.brands) return undefined;
    if (typeof deal.brands === 'object' && deal.brands && 'name' in deal.brands) {
      return deal.brands.name as string;
    }
    return undefined;
  };

  const getBrandLogo = () => {
    if (!deal.brands) return undefined;
    if (typeof deal.brands === 'object' && deal.brands && 'logo_url' in deal.brands) {
      return deal.brands.logo_url as string;
    }
    return undefined;
  };

  const getMerchantName = () => {
    if (!deal.merchants) return undefined;
    if (typeof deal.merchants === 'object' && deal.merchants && 'name' in deal.merchants) {
      return deal.merchants.name as string;
    }
    return undefined;
  };

  return {
    id: deal.id,
    title: deal.title,
    cleaned_title: deal.cleaned_title,
    discount: deal.discount,
    image_url: deal.image_url,
    imagebig_url: deal.imagebig_url,
    tracking_url: deal.tracking_url,
    merchant_name: getMerchantName(),
    brand_id: deal.brand_id,
    brand_name: getBrandName(),
    brand_logo_url: getBrandLogo(),
    is_featured: deal.is_featured || false,
    deal_end_date: deal.deal_end_date
  };
}) || [];

// Process flash deals data
const processedFlashDeals: Deal[] = flashDeals?.map((deal: any) => {
  // Safe access to nested properties
  const getBrandName = () => {
    if (!deal.brands) return undefined;
    if (typeof deal.brands === 'object' && deal.brands && 'name' in deal.brands) {
      return deal.brands.name as string;
    }
    return undefined;
  };

  const getBrandLogo = () => {
    if (!deal.brands) return undefined;
    if (typeof deal.brands === 'object' && deal.brands && 'logo_url' in deal.brands) {
      return deal.brands.logo_url as string;
    }
    return undefined;
  };

  const getMerchantName = () => {
    if (!deal.merchants) return undefined;
    if (typeof deal.merchants === 'object' && deal.merchants && 'name' in deal.merchants) {
      return deal.merchants.name as string;
    }
    return undefined;
  };

  return {
    id: deal.id,
    title: deal.title,
    cleaned_title: deal.cleaned_title,
    discount: deal.discount,
    image_url: deal.image_url,
    imagebig_url: deal.imagebig_url,
    tracking_url: deal.tracking_url,
    merchant_name: getMerchantName(),
    brand_id: deal.brand_id,
    brand_name: getBrandName(),
    brand_logo_url: getBrandLogo(),
    deal_end_date: deal.deal_end_date
  };
}) || [];

// Fetch top brands for grid
const { data: topBrands } = await supabase
  .from('brands')
  .select('*')
  .order('name')
  .limit(12);



// Fetch categories for explorer
const { data: categories } = await supabase
  .from('categories')
  .select('*')
  .order('name');

// Enhanced testimonials with authentic vape community language
const testimonials = [
  {
    quote: "Finally found a coupon site that gets the vape community! Saved over $200 on my ADV setup last month. Every code actually works - no more expired BS like other sites.",
    name: "Sarah K.",
    title: "Cloud Chaser",
    rating: 5,
    image: "/images/testimonials/sarah-k.jpeg", // WebP version will be automatically used by WebPImage component
    savedAmount: "$215.50",
    product: "SMOK Nord 4 Kit & Coil Bundle",
    location: "New York"
  },
  {
    quote: "These guys actually vape - you can tell from their deals. Found killer discounts on my favorite nic salts and the codes work every single time. No more hunting through expired coupons!",
    name: "Michael T.",
    title: "MTL Enthusiast",
    rating: 5,
    image: "/images/testimonials/michael-t.jpeg",
    savedAmount: "$178.25",
    product: "Vaporesso XROS 3 & Premium E-Juice",
    location: "Los Angeles"
  },
  {
    quote: "Been building coils for years and VapeHybrid always has the best deals on wire and cotton. Their exclusive codes beat going direct to manufacturers. Fellow vapers helping vapers!",
    name: "Jessica R.",
    title: "Coil Builder",
    rating: 4,
    image: "/images/testimonials/jessica-r.jpeg",
    savedAmount: "$320.75",
    product: "Geek Vape Aegis Legend 2 & Build Supplies",
    location: "Chicago"
  },
  {
    quote: "I run a local vape shop and send customers here when they want online deals. VapeHybrid knows their stuff - only legit codes from trusted brands. Real vapers supporting the community.",
    name: "David M.",
    title: "B&M Shop Owner",
    rating: 5,
    image: "/images/testimonials/david-m.jpeg",
    savedAmount: "$450.00",
    product: "Wholesale E-Liquid Stock",
    location: "Miami"
  },
  {
    quote: "Made the switch from stinkies last year and VapeHybrid saved me hundreds on starter gear. Their beginner-friendly deals helped me find my perfect all-day vape without breaking the bank.",
    name: "Robert J.",
    title: "Ex-Smoker",
    rating: 5,
    image: "/images/testimonials/robert-j.jpeg",
    savedAmount: "$85.30",
    product: "Uwell Caliburn G2 Starter Kit",
    location: "Seattle"
  },
  {
    quote: "Love the flash deals! Scored a Lost Mary at 40% off last week. The countdown timers keep me checking back - it's like Black Friday for vapers every day!",
    name: "Amanda P.",
    title: "Flavor Chaser",
    rating: 5,
    image: "/images/testimonials/amanda-p.jpg",
    savedAmount: "$125.45",
    product: "Lost Vape Ursa Quest Multi Kit",
    location: "Denver"
  }
];

// Format categories for category grid
const categoryItems = categories?.map((category: any) => ({
  title: category.name,
  description: `Browse all ${category.name.toLowerCase()} deals and coupons`,
  link: `/coupons/categories/${category.slug}`,
  image: category.category_logo
})) || [];

// Import FAQ data
import { faqs } from '../data/faqs';

// Comparison table data
const comparisonFeatures = [
  {
    feature: "Daily Deal Verification",
    vapehybrid: true,
    others: false
  },
  {
    feature: "Exclusive Coupon Codes",
    vapehybrid: true,
    others: "Sometimes"
  },
  {
    feature: "Number of Verified Brands",
    vapehybrid: "238+",
    others: "Varies"
  },
  {
    feature: "Independent Testing",
    vapehybrid: true,
    others: false
  },
  {
    feature: "User Reviews",
    vapehybrid: true,
    others: "Sometimes"
  },
  {
    feature: "Success Rate Tracking",
    vapehybrid: true,
    others: false
  },
  {
    feature: "Product Reviews",
    vapehybrid: true,
    others: "Limited"
  },
  {
    feature: "Giveaways",
    vapehybrid: true,
    others: "Rarely"
  },
  {
    feature: "Vape Industry News",
    vapehybrid: true,
    others: "No"
  }
];

// Why trust us stats - Kept for reference
// const trustStats = [
//   {
//     value: "6+ Years",
//     label: "Industry Experience"
//   },
//   {
//     value: "100+",
//     label: "Verified Brands"
//   },
//   {
//     value: "1000+",
//     label: "Active Deals"
//   },
//   {
//     value: "98%",
//     label: "Success Rate"
//   }
// ];

// Create offer structured data for featured deals
const offerStructuredData = processedDeals.map((deal, index) => {
  // Generate deterministic but natural-looking ratings based on deal ID
  const seed = Number(deal.id) || index;
  const ratingValue = (4.0 + (Math.sin(seed * 0.1) * 0.5 + 0.5) * 1.0).toFixed(1); // 4.0-5.0 range
  const ratingCount = Math.floor(15 + (Math.sin(seed * 0.2) * 0.5 + 0.5) * 85); // 15-100 range

  return {
    "@context": "https://schema.org",
    "@type": "Offer",
    "itemOffered": {
      "@type": "Product",
      "name": deal.cleaned_title || deal.title,
      "description": `${deal.cleaned_title || deal.title} - Save with this exclusive deal from VapeHybrid`,
      "brand": {
        "@type": "Brand",
        "name": deal.brand_name || "Vape Brand"
      },
      "image": deal.imagebig_url || deal.image_url || deal.brand_logo_url || "/placeholder-deal.jpg",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": ratingValue,
        "bestRating": "5",
        "worstRating": "1",
        "ratingCount": ratingCount.toString()
      }
    },
    "offeredBy": {
      "@type": "Organization",
      "name": deal.merchant_name || "VapeHybrid Partner"
    },
    "price": "0",
    "priceCurrency": "USD",
    "availability": "https://schema.org/InStock",
    "url": `${Astro.url.origin}/go/${deal.id}`,
    "seller": {
      "@type": "Organization",
      "name": "VapeHybrid"
    },
    "validFrom": new Date().toISOString(),
    "priceValidUntil": new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString()
  };
});

// FAQ structured data already declared above, using the first declaration

// Combine all structured data
const structuredData = [
  websiteStructuredData,
  organizationStructuredData,
  ...offerStructuredData,
  faqStructuredData
];
---

<HomeLayout title={title} description={description} structuredData={structuredData}>
  <!-- 1. Enhanced Hero Section - Vape community focused value proposition -->
  <EnhancedHero
    subtitle="Score verified coupon codes for your favorite vape gear! Fellow vapers test every code daily so you get working discounts on everything from all-day vapes to sub-ohm setups. No expired codes, just real savings for the vape community."
    ctaText="Find Vape Coupons"
    ctaLink="/coupons"
    secondaryCTAText="How We Verify Codes"
    secondaryCTALink="/how-it-works"
    client:load
  />

  <!-- 2. Category Navigation Bar - Quick access to deals by category -->
  <CategoryNav
    categories={categoryItems.map((cat: any) => ({
      title: cat.title,
      slug: cat.link.split('/').pop() || '',
    }))}
    client:load
  />

  <!-- Vape Community Reviews Section - After category navigation -->
  <Section class="home-section bg-transparent backdrop-blur-sm py-0" pattern="dots">
    <TestimonialsSection
      testimonials={testimonials}
      title="What Fellow Vapers Are Saying About Our Coupon Codes"
      client:visible
    />
  </Section>

  <!-- Real-Time Vape Community Savings - Social proof (moved up) -->
  <Section class="home-section bg-transparent backdrop-blur-sm py-0" pattern="dots">
    <RealTimeSavings
      title="Live Savings from the Vape Community"
      subtitle="Join thousands of fellow vapers scoring deals on their favorite gear with verified coupon codes"
      client:visible
    />
  </Section>

  <!-- 3. Featured Vape Coupon Codes Showcase with Flash Deals -->
  <Section class="home-section bg-transparent backdrop-blur-sm py-0" pattern="dots">
    <FeaturedDeals
      deals={processedDeals}
      flashDeals={processedFlashDeals}
      viewAllLink="/coupons"
      viewAllText="Browse All Vape Coupons"
      client:visible
    />
  </Section>





  <!-- 9. Verified Vape Brand Partners - Community favorites -->
  <SimplifiedBrandGrid
    brands={topBrands || []}
    title="Top Vape Brands with Exclusive Coupon Codes"
    subtitle="Score deals from community favorites like Geek Bar, Lost Mary, Voopoo, SMOK and more trusted names vapers love"
    viewAllLink="/coupons/brands"
    client:visible
  />

  <!-- 10. Comparison Table - Competitive advantage -->
  <Section class="home-section bg-transparent backdrop-blur-sm py-0" pattern="dots">
    <div class="max-w-4xl mx-auto px-4 py-20 flex flex-col items-center">
      <div class="text-center mb-10">
        <div class="inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-design-primary mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
          </svg>
          <span class="text-design-primary font-medium text-xs">Comparison</span>
        </div>

        <h2 class="text-3xl md:text-[34px] font-normal text-design-foreground mb-4">Why Vapers Choose VapeHybrid Over Other Coupon Sites</h2>
        <p class="text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto mb-8">
          We're vapers helping vapers - see why our community trusts us for working coupon codes
        </p>
      </div>

      <!-- Comparison Table -->
      <div class="bg-design-card/95 backdrop-blur-sm rounded-xl overflow-hidden border border-design-border">
        <div class="grid grid-cols-4 text-center border-b border-design-border">
          <div class="p-4 font-medium text-xs text-design-foreground">Features</div>
          <div class="p-4 font-bold text-xs text-design-primary">VapeHybrid</div>
          <div class="p-4 font-medium text-xs text-design-muted-foreground">Other Coupon Sites</div>
          <div class="p-4 font-medium text-xs text-design-muted-foreground">Vape Review Sites</div>
        </div>

        {comparisonFeatures.map((feature, index) => (
          <div class={`grid grid-cols-4 text-center ${index !== comparisonFeatures.length - 1 ? 'border-b border-design-border/50' : ''}`}>
            <div class="p-3 text-xs font-medium text-design-foreground">{feature.feature}</div>
            <div class="p-3">
              {typeof feature.vapehybrid === 'boolean' ? (
                feature.vapehybrid ? (
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-muted-foreground mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                )
              ) : (
                <span class="text-xs font-medium text-design-primary">{feature.vapehybrid}</span>
              )}
            </div>
            <div class="p-3">
              {typeof feature.others === 'boolean' ? (
                feature.others ? (
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-muted-foreground mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                )
              ) : (
                <span class="text-xs font-medium text-design-muted-foreground">{feature.others}</span>
              )}
            </div>
            <div class="p-3">
              {typeof feature.others === 'boolean' ? (
                feature.others ? (
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-muted-foreground mx-auto" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                )
              ) : (
                <span class="text-xs font-medium text-design-muted-foreground">{feature.others}</span>
              )}
            </div>
          </div>
        ))}
      </div>

      <!-- How We Verify Coupon Codes for the Vape Community -->
      <div class="bg-design-card/95 backdrop-blur-sm rounded-xl p-6 border border-design-border mt-12 mb-12">
        <h3 class="text-lg font-bold text-design-foreground mb-4 text-center">How We Verify Every Vape Coupon Code</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div class="flex items-start">
            <div class="bg-design-primary/10 rounded-full p-2 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <p class="text-xs text-design-foreground font-medium">Real vapers test every coupon code with actual purchases before we publish</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-design-primary/10 rounded-full p-2 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <p class="text-xs text-design-foreground font-medium">Our vape community team checks codes daily - expired ones get removed immediately</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-design-primary/10 rounded-full p-2 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            </div>
            <div>
              <p class="text-xs text-design-foreground font-medium">We only work with trusted vape brands and merchants that vapers actually use</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-design-primary/10 rounded-full p-2 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div>
              <p class="text-xs text-design-foreground font-medium">Our team are actual vapers - we understand what the community needs and wants</p>
            </div>
          </div>
        </div>
      </div>

      <!-- CTA Button - Larger Size -->
      <div class="text-center mt-12 mb-6">
        <a href="/about" class="inline-block text-sm font-medium transition-all duration-300 px-6 py-2.5 rounded-full
        bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white hover:text-white
        shadow-glow hover:shadow-glow-light hover:animate-glow-border
        hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move
        dark:bg-gradient-to-b dark:from-[#1df292] dark:to-[#0db875] dark:text-black
        dark:shadow-glow-dark dark:hover:shadow-glow-dark
        dark:hover:bg-animated-gradient-dark" style="min-width: 180px; text-align: center;">
          Why We're Different
        </a>
      </div>
    </div>
  </Section>



  <!-- 12. Vape Community Newsletter - Never miss deals -->
  <Section class="home-section bg-transparent backdrop-blur-sm py-0" pattern="dots">
    <NewsletterSection
      title="Never Miss a Vape Deal Again"
      subtitle="Join 50,000+ fellow vapers and get the hottest coupon codes delivered straight to your inbox"
      buttonText="Get Vape Deals"
      client:visible
    />
  </Section>

  <!-- 13. Vape Community FAQ Section - Address common vaper questions -->
  <Section class="home-section bg-transparent backdrop-blur-sm py-0" pattern="dots">
    <FAQSection
      title="Vape Coupon Questions from Fellow Vapers"
      subtitle="Everything the vape community wants to know about scoring the best deals on gear"
      faqs={faqs}
      client:visible
    />
  </Section>
</HomeLayout>
